import { useCreatePoStore, useGlobalStore } from "@bryzos/giss-ui-library"
import SearchHeader from "../../SearchHeader"
import styles from "./SavedBom.module.scss"
import CreatePo from "../createPo";
import { useEffect } from "react";
import { useState } from "react";
import BomReview from "../BomReview/BomReview";
const SavedBom = () => {
    const {createPoDataFromSavedBom, bomProductMappingDataFromSavedBom, setCreatePoDataFromSavedBom, setBomProductMappingDataFromSavedBom, isCreatePOModule, setIsCreatePOModule} = useCreatePoStore();
    const [showNoRecords, setShowNoRecords] = useState(true);
    
    useEffect(() => {
        return () => {
            setCreatePoDataFromSavedBom(null);
            setBomProductMappingDataFromSavedBom(null);
            setIsCreatePOModule(true);
        }
    },[])

    useEffect(() => {
        if(createPoDataFromSavedBom || bomProductMappingDataFromSavedBom){
            setShowNoRecords(false);
        }else{
            setShowNoRecords(true);
        }
    },[createPoDataFromSavedBom, bomProductMappingDataFromSavedBom])
    
    if(showNoRecords){
        return (
            <>
                <SearchHeader />
                <div className={styles.savedBomContainer}>
                    <div className={styles.noOrderDetailsContainer}>
                        <p>NO ORDER DETAILS TO DISPLAY</p>
                        <p>SELECT SAVED PO FROM THE LEFT</p>
                    </div>
                </div>
            </>
                
        )
    }
    return (
        <>
            {isCreatePOModule ? <CreatePo key={createPoDataFromSavedBom?.id} /> : <BomReview key={bomProductMappingDataFromSavedBom?.id} />}
        </>
    )
}

export default SavedBom;
