import React, { useMemo, useRef, useEffect } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  ColumnDef,
  flexRender,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import clsx from 'clsx';
import BomTile from '../bomTile/BomTile';

// Define the data structure for each row
interface BomRowData {
  id: string;
  lineNumber: number;
  lineStatusIndex: number;
  bom_line_id: string;
  // Add other properties as needed from the original data structure
  [key: string]: any;
}

interface VirtualizedBomTableProps {
  styles: any;
  createPoContainerRef: React.RefObject<HTMLDivElement>;
  formInputGroupRef: React.RefObject<HTMLDivElement>;
  orderInfoIsFilled: boolean;
  hidePoLineScroll: boolean;
  setHidePoLineScroll: (value: boolean) => void;
  filteredFields: any[];
  addPoLineTableRef: React.RefObject<HTMLDivElement>;
  bomUploadResult: any[];
  register: any;
  fields: any[];
  products: any[];
  setValue: any;
  watch: any;
  errors: any;
  control: any;
  getValues: any;
  pricePerUnitChangeHandler: any;
  userPartData: any;
  sessionId: string;
  searchStringData: any;
  setSearchString: any;
  setLineSessionId: any;
  lineSessionId: string;
  setDisableBidBuyNow: any;
  setOpenDeliveryToDialog: any;
  scrollToTop: any;
  clearErrors: any;
  currentBomData: any;
  updateLineProductTag: any;
  validateSavedBomCreatePo: any;
  scrollPoHeaderToBottom: any;
  setFocusJobPoInput: any;
  scrollerRef: React.RefObject<any>;
  lastModifiedBomRef: React.RefObject<HTMLElement>;
  lastModifiedBom: string | null;
}

const VirtualizedBomTable: React.FC<VirtualizedBomTableProps> = React.memo((props) => {
  const {
    styles,
    filteredFields,
    addPoLineTableRef,
    bomUploadResult,
    orderInfoIsFilled,
    hidePoLineScroll,
    watch,
    scrollerRef,
    ...bomTileProps
  } = props;

  // Transform filteredFields into the format expected by react-table
  const data = useMemo<BomRowData[]>(() => {
    return filteredFields.map((item: any, index: number) => ({
      id: item.bom_line_id || `row-${index}`,
      lineNumber: index + 1,
      lineStatusIndex: item.lineStatusIndex,
      bom_line_id: item.bom_line_id,
      originalItem: item,
      filteredItemIndex: index,
    }));
  }, [filteredFields]);

  // Define table columns
  const columns = useMemo<ColumnDef<BomRowData>[]>(() => [
    {
      id: 'bomRow',
      header: () => null, // We'll render the header separately
      cell: ({ row }) => {
        const rowData = row.original;
        const index = rowData.lineStatusIndex;

        // Only render if the index is valid
        if (index >= bomUploadResult?.length) {
          return null;
        }

        // Watch the specific values for optimization (same as original)
        const lineStatus = watch(`cart_items.${index}.lineStatus`);
        const domesticMaterialOnly = watch(`cart_items.${index}.domesticMaterialOnly`);
        const descriptionObj = watch(`cart_items.${index}.descriptionObj`);
        const productTag = watch(`cart_items.${index}.product_tag`);
        const qty = watch(`cart_items.${index}.qty`);
        const qtyUnit = watch(`cart_items.${index}.qty_unit`);

        return (
          <BomTile
            key={`bom-tile-${index}`}
            index={index}
            bomTileDefaultData={bomUploadResult[index] ?? []}
            filteredItemIndex={rowData.filteredItemIndex}
            filterFieldsLength={filteredFields.length}
            // Optimized props for BomTile rerendering
            lineStatus={lineStatus}
            domesticMaterialOnly={domesticMaterialOnly}
            descriptionObj={descriptionObj}
            productTag={productTag}
            qty={qty}
            qtyUnit={qtyUnit}
            viewLineStatusIndex={rowData.filteredItemIndex + 1}
            {...bomTileProps}
          />
        );
      },
    },
  ], [bomUploadResult, filteredFields, watch, bomTileProps]);

  // Create table instance
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  // Get table rows
  const { rows } = table.getRowModel();

  // Virtualization setup
  const parentRef = useRef<HTMLDivElement>(null);
  
  const virtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 214, // minLineItemHeight from CSS
    overscan: 10, // Render 10 extra items above and below visible area for smoother scrolling
    measureElement: (element) => {
      // Dynamically measure actual element height for better accuracy
      return element?.getBoundingClientRect().height ?? 214;
    },
  });

  // Memoize virtual items for better performance
  const virtualItems = useMemo(() => virtualizer.getVirtualItems(), [virtualizer]);

  // Handle scroll events (preserve original functionality)
  const handleLineItemScroll = () => {
    if (!parentRef.current) return;
    const { scrollTop } = parentRef.current;
    if (scrollerRef.current) {
      scrollerRef.current.updateScrollPosition(204 + scrollTop);
    }
  };

  // Handle click events (preserve original functionality)
  const openAddLineTab = () => {
    if (!parentRef.current) return;
    const container = parentRef.current;
    if (container.scrollTop > 0 && orderInfoIsFilled && props.createPoContainerRef.current) {
      props.createPoContainerRef.current.scrollTo({
        top: props.createPoContainerRef.current.scrollHeight,
        behavior: 'smooth'
      });
      container.style.overflowY = 'auto';
      const formInputHeight = props.formInputGroupRef.current?.clientHeight;
      setTimeout(() => {
        if (scrollerRef.current) {
          scrollerRef.current.updateScrollPosition(container.scrollTop + formInputHeight);
        }
      }, 400);
    }
  };

  // Sync refs
  useEffect(() => {
    if (addPoLineTableRef && parentRef.current) {
      (addPoLineTableRef as any).current = parentRef.current;
    }
  }, [addPoLineTableRef]);

  return (
    <div
      ref={parentRef}
      style={{ 
        overflowY: (!hidePoLineScroll && orderInfoIsFilled) ? 'auto' : 'hidden',
        height: '100%',
        maxHeight: 'calc(100% - 40px)',
      }}
      className={clsx(
        styles.uploadBOMLineTable, 
        (filteredFields?.length === 3) && styles.uploadBOMLineTableMinHeight
      )}
      onScroll={handleLineItemScroll}
      onClick={openAddLineTab}
    >
      {/* Table Header */}
      <table style={{ width: '100%' }}>
        <thead style={{
          position: 'sticky',
          top: 0,
          zIndex: 100,
          borderTop: '1px solid rgba(255, 255, 255, 0.2980392157)',
          backgroundImage: 'linear-gradient(102deg, #0f0f14 -8%, #393e47 275%)'
        }}>
          <tr>
            <th style={{
              width: '117px',
              padding: 0,
              textAlign: 'center',
              fontFamily: 'Syncopate',
              fontSize: '16px',
              fontWeight: 'bold',
              color: '#9b9eac',
              paddingBottom: '16px'
            }}>
              <span>LN</span>
            </th>
            <th style={{
              width: '292px',
              paddingTop: '16px',
              paddingLeft: '15px',
              fontFamily: 'Syncopate',
              fontSize: '16px',
              fontWeight: 'bold',
              color: '#9b9eac',
              paddingBottom: '16px'
            }}>
              <span>DESCRIPTION</span>
            </th>
            <th style={{
              width: '233px',
              paddingLeft: '41px',
              paddingTop: '16px',
              fontFamily: 'Syncopate',
              fontSize: '16px',
              fontWeight: 'bold',
              color: '#9b9eac',
              paddingBottom: '16px'
            }}>
              <span>QTY</span>
            </th>
            <th colSpan={2} style={{
              width: '158px',
              paddingTop: '16px',
              fontFamily: 'Syncopate',
              fontSize: '16px',
              fontWeight: 'bold',
              color: '#9b9eac',
              paddingBottom: '16px'
            }}>
              <span>LINE STATUS</span>
            </th>
          </tr>
        </thead>
      </table>

      {/* Virtualized Table Body */}
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualItems.map((virtualItem) => {
          const row = rows[virtualItem.index];
          if (!row) return null;

          return (
            <div
              key={virtualItem.key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualItem.size}px`,
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              <table style={{
                width: '100%',
                borderSpacing: '1px',
                borderCollapse: 'collapse'
              }}>
                <tbody>
                  {flexRender(row.getVisibleCells()[0].column.columnDef.cell, row.getVisibleCells()[0].getContext())}
                </tbody>
              </table>
            </div>
          );
        })}
      </div>
    </div>
  );

});

export default VirtualizedBomTable;
