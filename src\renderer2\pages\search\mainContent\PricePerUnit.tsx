import React, { useEffect, useState, useRef } from 'react';

import styles from '../home.module.scss';
import { useSearchStore } from '../../../store/SearchStore';
import { ProductPricingModel } from '../../../types/Search';
import { newPriceFormatter, priceFormatter } from '../../../helper';
import RenderOdometers from '../../../component/Odometer/RenderOdometers';
import clsx from 'clsx';
import { useGlobalStore } from '@bryzos/giss-ui-library';

type PricingFeedbackState = {
  product: ProductPricingModel
};

const PricePerUnit: React.FC<PricingFeedbackState> = ({ product }) => {
  const { selectedPriceUnit, searchZipCode, orderSizeSliderValue, selectedDomesticOption } = useSearchStore();
  const [formattedPrice, setFormattedPrice] = useState<string>("0.00");
  const { discountData, referenceDataUpdated } = useGlobalStore();

  // Ref to store previous price for comparison
  const prevPriceRef = useRef<string>("0.00");

  useEffect(() => {
      const fetchPrice = () => {
        const price = newPriceFormatter(product);
        setFormattedPrice(price.toString());
      };
      fetchPrice();
  }, [product, searchZipCode, selectedPriceUnit, selectedDomesticOption , discountData, referenceDataUpdated]);

  // Update the previous price after rendering
  useEffect(() => {
    prevPriceRef.current = formattedPrice;
  }, [formattedPrice]);

  return (
    <div className={clsx(styles.priceSelectedWrapper,'priceSelectedWrapper')}>
      <div className={clsx(styles.priceSelected,'priceSelected')}>
        <span className={clsx(styles.priceSection,'priceSection')}>
          <span className={styles.doller}>$</span>
          {/* Custom Odometers for each digit */}
          <div className={clsx(styles.displayRow,'displayRow')}>
            <RenderOdometers
              previous={prevPriceRef.current}
              current={formattedPrice}
            />
          </div>
        </span>

        <span className={styles.priceUnit}>
          <span className={styles.unitLbl}>PER </span> 
          {selectedPriceUnit.toUpperCase()}
        </span>
      </div>
    </div>
  );
}

export default PricePerUnit;
