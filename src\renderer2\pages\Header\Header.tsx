import React, { useEffect, useRef, useState, KeyboardEvent } from 'react'
import styles from '../Header/Header.module.scss'
import { ReactComponent as BLogo } from '../../assets/New-images/New-Bryzos-Logo.svg';
import { ReactComponent as PinIcon } from '../../assets/New-images/Pin.svg';
import { ReactComponent as PinIconHover } from '../../assets/New-images/Pin-Hover.svg';
import { ReactComponent as MenuIcon } from '../../assets/New-images/Menu.svg';
import { ReactComponent as MenuIconHover } from '../../assets/New-images/Menu-Hover.svg';
import { ReactComponent as NotificationIcon } from '../../assets/New-images/Notification.svg';
import { ReactComponent as NotificationIconHover } from '../../assets/New-images/Notification-Hover.svg';
import { ReactComponent as BackArrow } from '../../assets/New-images/Create-Account/arrow-left.svg';
import { useAuthStore, useCreatePoStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { routes, shareEmailTypes } from '../../common';
import { useLocation } from 'react-router-dom';
import { useLeftPanelStore } from '../../component/LeftPanel/LeftPanelStore';
import clsx from 'clsx';
import NewBryzosLogo from "../../assets/New-images/New-Logo.png";
import { MENU_ANIMATION_DURATION, navigatePage } from 'src/renderer2/helper';
import { useRightWindowStore } from '../RightWindow/RightWindowStore';
import ShareEmailWindow from 'src/renderer2/component/ShareEmailWindow/ShareEmailWindow';
import { useBomPdfExtractorStore } from '../buyer/BomPdfExtractor/BomPdfExtractorStore';

// Add interface for AuthStore
interface AuthStore {
  initiateLogout: (arg1: boolean, arg2: boolean, arg3: boolean) => void;
  [key: string]: any;
}

const Header = () => {
    const {initiateLogout} = useAuthStore() as AuthStore;
    const { setOpenLeftPanel, openLeftPanel } = useLeftPanelStore();
    const location = useLocation();
    const lockMenuTillAnimation = useRef(false);
    const { isCreatePOModule } = useCreatePoStore();
    const { setLoadComponent, loadComponent, setShareEmailWindowProps, setShareEmailType, shareEmailType } = useRightWindowStore();
    const { isSubscribeClickedDuringSignup, setIsSubscribeClickedDuringSignup, userData, userSubscription, appVersion } = useGlobalStore();
    const {showBackToBomUploadButton} = useBomPdfExtractorStore();


    const getTitle = () => {
        switch (location.pathname) {
            case routes.createPoPage:
            case routes.orderConfirmationPage:
                return 'CREATE PO';
            case routes.bomUploadReview:
                return  isCreatePOModule ? 'CREATE PO' : 'UPLOAD BOM';
            case routes.buyerSettingPage:
                return 'SETTINGS';
            case routes.homePage:
                return 'PRICE SEARCH';
            case routes.bomUpload:
                return 'CREATE PO';
            case routes.subscribe:
                return 'SUBSCRIBE';
            case routes.onboardingDetails:
            case routes.onboardingTnc:
                return 'CREATE ACCOUNT';
            case routes.onboardingThankYou:
                return 'BRYZOS';
            case routes.savedBom:
                return 'SAVED BOM';
            case routes.newSetting:
                return 'SETTINGS';
            case routes.bomExtractor:
                return 'UPLOAD BOM';
            // seller side Header
            case routes.sellerSettingPage:
                return 'SETTINGS';
            case routes.orderPage:
                return 'AVAILABLE ORDERS';
            case routes.acceptOrderPage:
                return 'CLAIM ORDER';
            case routes.impersonateList:
                return 'IMPERSONATE USER'
            case routes.videoLibrary:
                return 'VIDEO LIBRARY'
            // Add more cases as needed for other pages
            default:
                return 'DEFAULT TITLE'; // Fallback title
        }
    };

    const handleMenuClick = () => {
        if(lockMenuTillAnimation.current) return;
        setOpenLeftPanel(!openLeftPanel);
        lockMenuTillAnimation.current = true;
        setTimeout(() => {
            lockMenuTillAnimation.current = false;
        }, MENU_ANIMATION_DURATION);
    };

    // Add keydown handler for the menu button
    const handleMenuKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleMenuClick();
        }
    };
    const handleInviteUserClick = () => {
        setShareEmailType(shareEmailTypes.inviteUser);
        setShareEmailWindowProps({isSharePrice: false});
        setLoadComponent(<ShareEmailWindow />)
    };

    const handleInviteUserKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleInviteUserClick();
        }
    };

    const handleSubscribeClick = () => {
        setIsSubscribeClickedDuringSignup(true);
    }

    return (
        <div className={styles.headerStyle}>
            {(!userSubscription || !userSubscription?.id) &&
            <>
           {(location.pathname !== routes.onboardingDetails && location.pathname !== routes.onboardingTnc && location.pathname !== routes.onboardingThankYou && userData?.data?.type !== 'SELLER') ?
                <div className={clsx(styles.subscribeTextDiv, location.pathname === routes.homePage && styles.priceSearchSubscribeTextDiv)}>
                    <span className={styles.subscribeText}>SUBSCRIBE</span>
                    <span className={styles.freeTrialText1}>FREE TRIAL: 14 DAYS LEFT</span>
                </div>
                :
                userData?.data?.type !== 'SELLER' && <div className={styles.subscribeTextDiv}>
                    <span className={styles.subscribeText1}>You are starting <br /> a free 30 day trial</span>
                </div>}
            </>
            }
            <span className='dragArea'></span>
            <div className={styles.headerMain}>
                <div className={clsx(styles.logoSection, location.pathname === routes.homePage && styles.priceSearchMargin)}><img className={styles.bryzosLogo} src={NewBryzosLogo} alt="Logo" /></div>
                <div className={styles.pageName}><div>{getTitle()}</div> <div onClick={() => { initiateLogout(false, false, true)}} className={styles.greenDot}></div> 
                  {(import.meta.env.VITE_ENVIRONMENT === 'staging' || import.meta.env.VITE_ENVIRONMENT === 'dev') ? <span className={styles.stagingEnv}>&nbsp;Staging&nbsp;v{appVersion}</span> : 
                    (import.meta.env.VITE_ENVIRONMENT === 'qa')?<span className={styles.qaEnv}>&nbsp;QA&nbsp;v{appVersion}</span>:
                    (import.meta.env.VITE_ENVIRONMENT === 'demo' && <span className={styles.demoEnv}>&nbsp;DEMO&nbsp;v{appVersion}</span>)}
                
                </div>
                <div>{showBackToBomUploadButton &&
                    <button className={styles.bomBackpage} onClick={() => { navigatePage(location.pathname, { path: routes.bomExtractor })}}><BackArrow/>Back to Bom Upload</button>}
                </div>
                {location.pathname !== routes.onboardingDetails && location.pathname !== routes.onboardingTnc && location.pathname !== routes.onboardingThankYou ?
                    <div className={clsx(styles.iconMain)}>
                        {/* {location.pathname === routes.homePage &&
                        <>
                            <div className={styles.iconDiv} id='toggle-sticky-btn'>
                                <span className={styles.iconDivImg1}><PinIcon /></span>
                                <span className={styles.iconDivImg2}><PinIconHover /></span>
                            </div>
                            <div className={styles.iconDiv}>
                                <span className={styles.iconDivImg1}><NotificationIcon /></span>
                                <span className={styles.iconDivImg2}><NotificationIconHover /></span>
                            </div>
                            <div className={styles.iconTopVc} onClick={() => window?.electron?.send({ channel: channelWindow.minimize })}>
                                <span >VC</span>
                            </div>
                        </>
                    } */}
                        <div tabIndex={11} className={clsx(styles.iconTopInviteUser, (shareEmailType === shareEmailTypes.inviteUser) && styles.activeIconTopInviteUser)} onClick={handleInviteUserClick} onKeyDown={handleInviteUserKeyDown}>
                            <span>INVITE USERS</span>
                        </div>
                        <div
                            className={styles.iconDiv}
                            tabIndex={12}
                            onClick={() => handleMenuClick()}
                            onKeyDown={handleMenuKeyDown}
                            role="button"
                            aria-label="Toggle menu"
                        >
                            <span className={styles.iconDivImg1}><MenuIcon /></span>
                            <span className={styles.iconDivImg2}><MenuIconHover /></span>
                        </div>
                    </div>
                    :
                    <div className={clsx(styles.iconMain, styles.iconTopSubscribe)}>
                        <div  tabIndex={11} className={clsx(styles.iconTopInviteUser, isSubscribeClickedDuringSignup && styles.activeIconTopInviteUser)} onClick={handleSubscribeClick}>
                            <span>SUBSCRIBE</span>
                        </div>
                    </div>
                }
            </div>
        </div>
    )
}

export default Header