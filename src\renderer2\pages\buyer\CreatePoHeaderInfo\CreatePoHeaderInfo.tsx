import { dateTimeFormat, InstantPurchasingWrapper, mobileDiaglogConst, useBuyerSettingStore, useCreatePoStore, useGetDeliveryDate, useGlobalStore, useStateZipValidation } from '@bryzos/giss-ui-library';
import React, { useEffect, useMemo, useRef, useState, forwardRef, useImperativeHandle } from 'react'
import { useLocation, useNavigate } from 'react-router-dom';
import { navigationConfirmMessages, routes } from 'src/renderer2/common';
import clsx from 'clsx';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import Calendar from 'src/renderer2/component/Calendar/Calendar';
import { Autocomplete, ClickAwayListener, Fade, TextField, Tooltip } from '@mui/material';
import { Controller } from 'react-hook-form';
import StateDropDown from 'src/renderer2/component/StateDropDown/StateDropDown';
import { ReactComponent as ChooseOneIcon } from '../../../assets/New-images/Choose-One.svg';
import { ReactComponent as UploadBOMIcon } from '../../../assets/New-images/Upload-BOM.svg';
import { ReactComponent as UploadBOMIconHover } from '../../../assets/New-images/Bom-Upload-Hover.svg';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { useLeftPanelStore } from 'src/renderer2/component/LeftPanel/LeftPanelStore';
import dayjs from 'dayjs';
import { useGenericForm } from 'src/renderer2/hooks/useGenericForm';
import { poHeaderSchema } from 'src/renderer2/models/poHeader.model';
import useMutateGetDeliveryAddress from 'src/renderer2/hooks/useMutateGetDeliveryAddress';

interface CreatePoHeaderInfoRef {
    orderInfoIsFilled: boolean;
}

const CreatePoHeaderInfo = forwardRef<CreatePoHeaderInfoRef, any>((
    {
        styles,
        formInputGroupRef,
        isCalendarOpen,
        setIsCalendarOpen,
        setOpenErrorDialog,
        setErrorMessage,
        saveUserActivity,
        saveBomHeaderDetails,
        disableBidBuyNow,
        setOpenDeliveryToDialog,
        openDeliveryToDialog,
        scrollToTop,
        isSavedBom,
        focusJobPoInput
    }, ref) => {
    const navigate = useNavigate();
    const location = useLocation();
    const { setShowLoader, backNavigation, setCreatePoSessionId, resetHeaderConfig, userData, setBackNavigation, bryzosPayApprovalStatus, setBryzosPayApprovalStatus, referenceData, productData, productMapping, discountData, referenceDataUpdated } = useGlobalStore();
    const { bomProductMappingSocketData, setBomProductMappingSocketData, isCreatePOModule, setIsCreatePOModule, setIsCreatePoDirty, isCreatePoDirty, setCreatePoData, createPoData, bomProductMappingDataFromSavedBom, createPoDataFromSavedBom, setCreatePoDataFromSavedBom, setBomProductMappingDataFromSavedBom, setBomDataIdToRefresh, bomDataIdToRefresh, bomSummaryViewFilter, setBomSummaryViewFilter, setUploadBomInitialData, uploadBomInitialData } = useCreatePoStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const { setOpenLeftPanel, setDisplayLeftPanel } = useLeftPanelStore();
    const containerRef = useRef(null);
    const fileInputRef = useRef(null); // Add ref for file input
    const [isFocused, setIsFocused] = useState(false);
    const [stateInputFocus, setStateInputFocus] = useState(false);
    const [autocompleteOpen, setAutocompleteOpen] = useState(false);
    const [autocompleteOpenLine2, setAutocompleteOpenLine2] = useState(false);
    const [autocompleteOpenCity, setAutocompleteOpenCity] = useState(false);
    const { mutateAsync: getDeliveryAddresses, data: deliveryAddressData } = useMutateGetDeliveryAddress();
    const [stateDropDownValue, setStateDropDownValue] = useState<any>('');
    const { buyerSetting } = useBuyerSettingStore();
    const getDeliveryDate = useGetDeliveryDate();
    const [deliveryDateMap, setDeliveryDateMap] = useState({});
    const [deliveryDates, setDeliveryDates] = useState([]);
    const [disableDeliveryDate, setDisableDeliveryDate] = useState(false);
    const [states, setStates] = useState([]);
    const jobPoInputRef = useRef(null);
    const {
        control,
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        reset,
        trigger,
        errors, 
        isValid
    } = useGenericForm(poHeaderSchema);

    const checkStateZipValidation = useStateZipValidation();

    const orderInfoIsFilled = useMemo(() => {
        return !!watch('shipping_details.line1') &&
            !!watch('shipping_details.city') &&
            !!watch('shipping_details.state_id') &&
            !!watch('shipping_details.zip') &&
            !!watch('internal_po_number') &&
            !!watch('order_type') &&
            !!watch('delivery_date') &&
            !errors?.shipping_details?.zip?.message
    }, [
        watch('shipping_details.line1'),
        watch('shipping_details.city'),
        watch('shipping_details.state_id'),
        watch('shipping_details.zip'),
        watch('internal_po_number'),
        watch('order_type'),
        watch('delivery_date'),
        errors?.shipping_details?.zip?.message
    ]);

    // Expose orderInfoIsFilled to parent component
    useImperativeHandle(ref, () => ({
        orderInfoIsFilled,
        initializePoHeaderForm,
        getHeaderFormData,
        watch
    }), [orderInfoIsFilled]);

    useEffect(() => {
        if(focusJobPoInput){
            setTimeout(() => {
                jobPoInputRef?.current?.focus();
            }, 100)
        }
    }, [focusJobPoInput])

    useEffect(() => {
        if(referenceData){
            setStates(referenceData?.ref_states)
        }
    }, [referenceData])

    useEffect(() => {
        getDeliveryAddresses();
        function handleClickOutside(event) {
            if (containerRef.current && !containerRef.current.contains(event.target)) {
                setIsFocused(false);
            }
        }
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    useEffect(() => {
        if(!(isSavedBom && (createPoDataFromSavedBom || watch('is_draft_po')))){
            onStateZipChange()
        }
    }, [watch('shipping_details.zip'), watch('shipping_details.state_id')])

    useEffect(() => {
        if(buyerSetting)setRecevingHoursAndDeliveryDateOffset()
    }, [buyerSetting])

    const handleJobPoInputRef = (e: any) => {
        jobPoInputRef.current = e;
    }

    const initializePoHeaderForm = async (initialData: any) => {
        setValue('internal_po_number', initialData.internal_po_number);
        setValue('shipping_details', initialData.shipping_details);
        setValue('delivery_date', initialData.delivery_date);
        setValue('order_type', initialData.order_type);
        if(initialData?.delivery_date_offset)
        setValue('delivery_date_offset', initialData.delivery_date_offset)
        setStateDropDownValue(referenceData?.ref_states?.find((state: any) => state.id === initialData?.shipping_details?.state_id)?.code || '')
        const deliveryDateList = await getDeliveryDateData();
        let disableDeliveryDates = true;
        deliveryDateList.forEach(deliveryDateObj => {
            // if(deliveryDateObj.days_to_add === deliveryDaysAddValue){
            //     const deliveryDate = !deliveryDateObj.disabled ? deliveryDateObj.value : null;
            //     setValue('delivery_date', dayjs(deliveryDate).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit));
            // }
            if (!deliveryDateObj.disabled && disableDeliveryDates) {
                disableDeliveryDates = false;
            }
        });
        setDisableDeliveryDate(disableDeliveryDates)
    }

    const getDeliveryDateData = async () => {
        let deliveryDateList = [];
        const res = await getDeliveryDate.mutateAsync();
        if (res?.data?.data) {
            deliveryDateList = res.data.data
        }
        const optionMap = deliveryDateList.reduce((acc, option) => {
            acc[option.value] = option;
            return acc;
        }, {});
        setDeliveryDateMap(optionMap)
        setDeliveryDates(deliveryDateList);
        return deliveryDateList
    }

    const onStateZipChange = async () => {
        await handleStateZipValidation();
    }

    const setRecevingHoursAndDeliveryDateOffset = () => {
        const deliveryReceivingHours = buyerSetting.user_delivery_receiving_availability_details;
        if (deliveryReceivingHours?.length !== 0) {
            setValue('recevingHours', deliveryReceivingHours)
        }
        const deliveryDaysAddValue = buyerSetting.delivery_days_add_value ?? referenceData?.ref_delivery_date[0]?.days_to_add;
        setValue('delivery_date_offset', deliveryDaysAddValue);
    }

    const getHeaderFormData = () => {
        return {
            internal_po_number: watch('internal_po_number'),
            shipping_details: watch('shipping_details'),
            delivery_date: watch('delivery_date'),
            order_type: watch('order_type')
        }
    }

    const handleStateZipValidation = async (): Promise<Boolean> => {
        const zipCode = watch("shipping_details.zip");
        const stateCode = watch("shipping_details.state_id");
        let isValid:Boolean = false;
        if (zipCode) {
          setValue('shipping_details.validating_state_id_zip', true);
            if (zipCode.length > 4 && stateCode) {
                const checkStateZipResponse: any = await checkStateZipValidation.mutateAsync({ zipCode, stateCode });
                 if (checkStateZipResponse.data.data === true) {
                    clearErrors(["shipping_details.zip","shipping_details.state_id"]);
                    isValid = true;
                } else {
                    showStateZipError();
                }
            } else {
                setError("shipping_details.zip", { message: "Zip Code is not valid" });
            }
          setValue('shipping_details.validating_state_id_zip', false);
        }
        return isValid;
      };

      const showStateZipError = () => {
        const zipCode = "shipping_details.zip";
        const stateCode = "shipping_details.state_id";
        setError(stateCode, { message: "The State and Zipcode do not match." });
        setError(zipCode, { message: "The State and Zipcode do not match." });
      }

    const handleOpenCalendarBtn = () => {
        if (!disableDeliveryDate) {
            setIsCalendarOpen(true);
        } else {
            setOpenErrorDialog(true)
            setErrorMessage(mobileDiaglogConst.receivingHrsEmpty)
            setIsCalendarOpen(false)
            const nextElement = document.querySelector('[tabindex="15"]');
            if (nextElement instanceof HTMLElement) {
                nextElement.focus();
            }
        }
    }


    const allowedDates = useMemo(() => {
        return deliveryDates
            .filter(date => !date?.disabled)
            .map(date => new Date(date?.value));
    }, [deliveryDates]);

    const handleDateSelect = (date: any) => {
        const selectedDate = dayjs(date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
        setValue('delivery_date_offset', deliveryDateMap?.[selectedDate]?.days_to_add);
        setIsCreatePoDirty(true)
    }


    const handleDeliveryInfoContainerClickAway = () => {
        if (errors?.shipping_details?.zip?.message || errors?.shipping_details?.state_id?.message || errors?.shipping_details?.city?.message || errors?.shipping_details?.line1?.message || errors?.shipping_details?.line2?.message) {
            setOpenDeliveryToDialog(true)
            setIsFocused(true)
        } else {
            setStateInputFocus(false)
            setOpenDeliveryToDialog(false)
            setIsFocused(false)
        }
    }

    const handleDeliveryToClick = () => {
        setOpenDeliveryToDialog(true);
        setIsFocused(true);
    }

    const handleAutocompleteTabSelection = (
        e: any,
        field: any,
        isOpen: boolean,
        filterFields: string[],
        setOpenState: (open: boolean) => void,
        nextFieldSelector: string
    ) => {
        if (e.key === 'Tab' && !e.shiftKey) {
            // Handle Tab key to select highlighted option
            if (isOpen && deliveryAddressData?.length > 0) {
                e.preventDefault();
                const activeElement = document.activeElement;
                const activeDescendantId = activeElement?.getAttribute('aria-activedescendant');

                if (activeDescendantId) {
                    // Extract option index from the ID
                    const optionIndexMatch = activeDescendantId.match(/option-(\d+)$/);
                    if (optionIndexMatch && optionIndexMatch[1]) {
                        const optionIndex = parseInt(optionIndexMatch[1]);
                        // Get filtered options based on current input
                        const inputValue = field.value || '';
                        const filteredOptions = deliveryAddressData.filter(option => {
                            return filterFields.some(filterField => {
                                const fieldValue = option?.[filterField];
                                return fieldValue?.toLowerCase().includes(inputValue.toLowerCase());
                            });
                        });

                        // Select the highlighted option
                        if (optionIndex >= 0 && optionIndex < filteredOptions.length) {
                            const selectedOption = filteredOptions[optionIndex];
                            // Auto-populate all shipping details fields
                            setValue('shipping_details.line1', selectedOption.line1);
                            setValue('shipping_details.line2', selectedOption.line2 || '');
                            setValue('shipping_details.city', selectedOption.city);
                            setValue('shipping_details.state_id', selectedOption.state_id);
                            setValue('shipping_details.zip', selectedOption.zip);
                            const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                            setStateDropDownValue(stateName);
                            setIsCreatePoDirty(true);
                            saveUserActivity();
                            saveBomHeaderDetails();
                            setOpenState(false);

                            // Focus on next field after selection
                            setTimeout(() => {
                                const nextInput = document.querySelector(nextFieldSelector);
                                if (nextInput instanceof HTMLElement) {
                                    nextInput.focus();
                                }
                            }, 100);
                        }
                    }
                }
            }
        }
    };

    const formatAddressDisplay = (address: any) => {
        if (!address) return '';
        const stateName = referenceData?.ref_states?.find(state => state.id === address?.state_id)?.code || '';

        const parts = [address.line1];
        if (address.line2 && address.line2.trim()) {
            parts.push(address.line2);
        }

        parts.push(address.city);
        parts.push(stateName);
        parts.push(address.zip);

        return parts.filter(Boolean).join(', ');
    };


    const handleUploadClick = () => {
        if (orderInfoIsFilled) {
            const { delivery_date, shipping_details, order_type, internal_po_number, delivery_date_offset } = getValues();
            const formattedCreatePoData = {
                delivery_date,
                shipping_details,
                order_type,
                internal_po_number,
                delivery_date_offset
            }
            // setCreatePoData(formattedCreatePoData)
            setUploadBomInitialData(formattedCreatePoData)
            const isDirty = watch('cart_items')?.some(item => item.descriptionObj && Object.keys(item.descriptionObj).length > 0);
            const message = navigationConfirmMessages.confirmLeave;
            if (isDirty) {
                showCommonDialog(null, message, null, resetDialogStore, [{ name: 'Yes', action: handleFileInputClick }, { name: 'No', action: resetDialogStore }])
            }
            else {
                handleFileInputClick();
            }
        }
    };

    const handleFileInputClick = () => {
        fileInputRef.current?.click();
        resetDialogStore();
    }

    const handleFileUpload = (event) => {
        const file = event.target.files[0];
        if (file) {
            if (!file.type.includes('pdf')) {
                showCommonDialog(null, "Please select a PDF file.", commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
                return;
            }
            navigate(routes.bomExtractor, { state: { file } });
        }
    };
    return (
        <div className={clsx(styles.formInputGroup, isCalendarOpen && styles.isCalendarOpenDiabledInput)} ref={formInputGroupRef}>
            {isCalendarOpen && <div className={styles.calendarOpenOverlay}></div>}

            <div className={styles.formInputGroup1}>
                <InputWrapper>
                    <CustomTextField className={clsx(styles.inputfiled, styles.pOInput)} type='text' register={register("internal_po_number")}
                        placeholder='JOB / PO#'
                        onBlur={(e) => {
                            e.target.value = e.target.value.trim();
                            register("internal_po_number").onBlur(e);
                            saveUserActivity();
                            saveBomHeaderDetails();
                        }}
                        onChange={(e) => {
                            setIsCreatePoDirty(true)
                        }}
                        maxLength={20}
                        tabIndex={13}
                        inputRef={handleJobPoInputRef}
                        autoFocus={location.pathname !== routes.savedBom}
                    />
                </InputWrapper>
                <div className={styles.deliverByContainer}>
                    <Calendar allowedDates={allowedDates}
                        value={watch('delivery_date')}
                        setValue={setValue}
                        isCalendarOpen={isCalendarOpen}
                        setIsCalendarOpen={setIsCalendarOpen}
                        disableDeliveryDate={disableDeliveryDate}
                        handleOpenCalendar={handleOpenCalendarBtn}
                        saveUserActivity={saveUserActivity}
                        onDateSelect={handleDateSelect}
                        saveBomHeaderDetails={saveBomHeaderDetails}
                    />
                </div>
                <div className={styles.radioGroupContainer}>
                    <span className={styles.chosseOneIcon}><ChooseOneIcon /></span>
                    <label
                        tabIndex={15}
                        className={clsx(`${styles.radioButtonLeft} ${(watch('order_type') === "BID") ? clsx(styles.selected, styles.bidSelected) : ""}`, styles.radioButton, disableBidBuyNow && styles.disableBidBuyNowBtn)}
                        onClick={() => {
                            if (!disableBidBuyNow) {
                                setValue('order_type', 'BID')
                                setIsCreatePoDirty(true)
                            }
                        }}
                        onKeyDown={(event) => {
                            if (event.key === 'Enter' && !disableBidBuyNow) {
                                setValue('order_type', 'BID');
                                setIsCreatePoDirty(true)
                            }
                        }}
                    >
                        <input
                            type="radio"
                            {...register("order_type")}
                            value="BID"
                            className={styles.hiddenRadio}
                            onChange={(e) => {
                                register('order_type').onChange(e)
                                setIsCreatePoDirty(true)
                                saveUserActivity()
                                saveBomHeaderDetails()
                            }}
                            disabled={disableBidBuyNow}
                        />
                        BID
                    </label>
                    <label
                        tabIndex={16}
                        className={clsx(`${styles.radioButtonRight} ${(watch('order_type') === "BUY") ? clsx(styles.selected, styles.buySelected) : ""}`, styles.radioButton, disableBidBuyNow && styles.disableBidBuyNowBtn)}
                        onClick={() => {
                            if (!disableBidBuyNow) {
                                setValue('order_type', 'BUY')
                            }
                        }}
                        onKeyDown={(event) => {
                            if (event.key === 'Enter' && !disableBidBuyNow) {
                                setValue('order_type', 'BUY');
                            }
                        }}
                    >
                        <input
                            type="radio"
                            {...register("order_type")}
                            value="BUY"
                            className={styles.hiddenRadio}
                            onChange={(e) => {
                                register('order_type').onChange(e)
                                saveUserActivity()
                                saveBomHeaderDetails()
                                setIsCreatePoDirty(true)
                            }}
                            disabled={disableBidBuyNow}
                        />
                        BUY
                    </label>
                </div>
            </div>
            <div className={styles.formInputGroup1}>
                <ClickAwayListener onClickAway={() => handleDeliveryInfoContainerClickAway()}>
                    <div
                        className={`${styles.deliverToContainer} ${isFocused ? styles.boxShadow : ""}`}
                        ref={containerRef}
                        onClick={handleDeliveryToClick}
                        tabIndex={17}
                        onFocus={() => {
                            setOpenDeliveryToDialog(true);
                            setIsFocused(true)
                        }}
                    >
                        {openDeliveryToDialog ?
                            <span className={styles.deliverToLabel}>
                                <>
                                    <Controller
                                        name="shipping_details.line1"
                                        control={control}
                                        render={({ field }) => (
                                            <Autocomplete
                                                disableClearable
                                                className={clsx(styles.autocompleteContainer, styles.line1Input)}

                                                options={deliveryAddressData || []}
                                                value={null}
                                                inputValue={field.value || ''}
                                                open={autocompleteOpen && (field.value?.length || 0) > 0}
                                                onOpen={() => {
                                                    if ((field.value?.length || 0) > 0) {
                                                        setAutocompleteOpen(true);
                                                    }
                                                }}
                                                onClose={() => setAutocompleteOpen(false)}
                                                getOptionLabel={(option) => {
                                                    if (typeof option === 'string') return option;
                                                    if (!option) return '';
                                                    return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                }}
                                                isOptionEqualToValue={(option, value) => {
                                                    return option?.id === value?.id;
                                                }}
                                                filterOptions={(options, { inputValue }) => {
                                                    if (!inputValue) return options;
                                                    const filtered = options.filter(option =>
                                                        option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                        option?.line2?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                        option?.city?.toLowerCase().includes(inputValue.toLowerCase())
                                                    );
                                                    return filtered;
                                                }}
                                                classes={{
                                                    paper: styles.autocompleteDropdown
                                                }}
                                                onInputChange={(event, newInputValue) => {
                                                    field.onChange(newInputValue);
                                                    setIsCreatePoDirty(true);
                                                    // Control when dropdown opens based on input length
                                                    if (newInputValue.length > 0) {
                                                        setAutocompleteOpen(true);
                                                    } else {
                                                        setAutocompleteOpen(false);
                                                    }
                                                }}
                                                onChange={(event, selectedOption) => {
                                                    if (selectedOption && typeof selectedOption === 'object') {
                                                        // Auto-populate all shipping details fields
                                                        setValue('shipping_details.line1', selectedOption.line1);
                                                        setValue('shipping_details.line2', selectedOption.line2 || '');
                                                        setValue('shipping_details.city', selectedOption.city);
                                                        setValue('shipping_details.state_id', selectedOption.state_id);
                                                        setValue('shipping_details.zip', selectedOption.zip);
                                                        const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                                                        setStateDropDownValue(stateName)
                                                        setIsCreatePoDirty(true);
                                                        saveUserActivity();
                                                        saveBomHeaderDetails();
                                                    }
                                                }}
                                                renderInput={(params) => (
                                                    <TextField
                                                        autoFocus
                                                        {...params}
                                                        className={clsx(styles.companyNameInput, styles.muiAutocompleteTextField)}
                                                        type='text'
                                                        placeholder='ADDRESS 1'
                                                        onBlur={(e) => {
                                                            e.target.value = e.target.value.trim();
                                                            field.onBlur();
                                                            saveUserActivity();
                                                            saveBomHeaderDetails();
                                                        }}
                                                        onKeyDown={(e) => {
                                                            if (e.key === 'Tab') {
                                                                if (e.shiftKey) {
                                                                    const nextElement = document.querySelector('[tabindex="16"]');
                                                                    if (nextElement instanceof HTMLElement) {
                                                                        nextElement.focus();
                                                                    }
                                                                    setOpenDeliveryToDialog(false);
                                                                    setIsFocused(false)
                                                                } else {
                                                                    handleAutocompleteTabSelection(
                                                                        e,
                                                                        field,
                                                                        autocompleteOpen,
                                                                        ['line1', 'line2', 'city'],
                                                                        setAutocompleteOpen,
                                                                        'input[name="shipping_details.line2"]'
                                                                    );
                                                                }
                                                            }
                                                        }}
                                                    />
                                                )}
                                                renderOption={(props, option) => (
                                                    <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                        <div>
                                                            {formatAddressDisplay(option)}
                                                        </div>
                                                    </li>
                                                )}
                                                freeSolo
                                                clearOnEscape
                                                noOptionsText=""
                                            />
                                        )}
                                    />
                                    <Controller
                                        name="shipping_details.line2"
                                        control={control}
                                        render={({ field }) => (
                                            <Autocomplete
                                                disableClearable
                                                options={deliveryAddressData || []}
                                                value={null}
                                                inputValue={field.value || ''}
                                                open={autocompleteOpenLine2 && (field.value?.length || 0) > 0}
                                                onOpen={() => {
                                                    if ((field.value?.length || 0) > 0) {
                                                        setAutocompleteOpenLine2(true);
                                                    }
                                                }}
                                                onClose={() => setAutocompleteOpenLine2(false)}
                                                getOptionLabel={(option) => {
                                                    if (typeof option === 'string') return option;
                                                    if (!option) return '';
                                                    return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                }}
                                                isOptionEqualToValue={(option, value) => {
                                                    return option?.id === value?.id;
                                                }}
                                                filterOptions={(options, { inputValue }) => {
                                                    if (!inputValue) return options;
                                                    return options.filter(option =>
                                                        option?.line2?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                        option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                        option?.city?.toLowerCase().includes(inputValue.toLowerCase())
                                                    );
                                                }}
                                                onInputChange={(event, newInputValue) => {
                                                    field.onChange(newInputValue);
                                                    setIsCreatePoDirty(true);
                                                    // Control when dropdown opens based on input length
                                                    if (newInputValue.length > 0) {
                                                        setAutocompleteOpenLine2(true);
                                                    } else {
                                                        setAutocompleteOpenLine2(false);
                                                    }
                                                }}
                                                onChange={(event, selectedOption) => {
                                                    if (selectedOption && typeof selectedOption === 'object') {
                                                        // Auto-populate all shipping details fields
                                                        setValue('shipping_details.line1', selectedOption.line1);
                                                        setValue('shipping_details.line2', selectedOption.line2 || '');
                                                        setValue('shipping_details.city', selectedOption.city);
                                                        setValue('shipping_details.state_id', selectedOption.state_id);
                                                        setValue('shipping_details.zip', selectedOption.zip);
                                                        const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                                                        setStateDropDownValue(stateName);
                                                        setIsCreatePoDirty(true);
                                                        saveUserActivity();
                                                        saveBomHeaderDetails();
                                                    }
                                                }}
                                                classes={{
                                                    paper: styles.autocompleteDropdown
                                                }}
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        className={clsx(styles.addressInputs, styles.muiAutocompleteTextField)}
                                                        type='text'
                                                        placeholder='ADDRESS 2'
                                                        onBlur={(e) => {
                                                            e.target.value = e.target.value.trim();
                                                            field.onBlur();
                                                            saveUserActivity();
                                                            saveBomHeaderDetails();
                                                        }}
                                                        onKeyDown={(e) => {
                                                            handleAutocompleteTabSelection(
                                                                e,
                                                                field,
                                                                autocompleteOpenLine2,
                                                                ['line2', 'line1', 'city'],
                                                                setAutocompleteOpenLine2,
                                                                'input[name="shipping_details.city"]'
                                                            );
                                                        }}
                                                    />
                                                )}
                                                renderOption={(props, option) => (
                                                    <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                        <div>
                                                            {formatAddressDisplay(option)}
                                                        </div>
                                                    </li>
                                                )}
                                                freeSolo
                                                clearOnEscape
                                                noOptionsText=""
                                            />
                                        )}
                                    />
                                </>
                                <span className={styles.lastAddressFiled}>
                                    <span className={styles.addressInputsCol1} >
                                        <Controller
                                            name="shipping_details.city"
                                            control={control}
                                            render={({ field }) => (
                                                <Autocomplete
                                                    disableClearable
                                                    className={styles.autocompleteContainer}
                                                    options={deliveryAddressData || []}
                                                    value={null}
                                                    inputValue={field.value || ''}
                                                    open={autocompleteOpenCity && (field.value?.length || 0) > 0}
                                                    onOpen={() => {
                                                        if ((field.value?.length || 0) > 0) {
                                                            setAutocompleteOpenCity(true);
                                                        }
                                                    }}
                                                    onClose={() => setAutocompleteOpenCity(false)}
                                                    getOptionLabel={(option) => {
                                                        if (typeof option === 'string') return option;
                                                        if (!option) return '';
                                                        return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                    }}
                                                    isOptionEqualToValue={(option, value) => {
                                                        return option?.id === value?.id;
                                                    }}
                                                    filterOptions={(options, { inputValue }) => {
                                                        if (!inputValue) return options;
                                                        return options.filter(option =>
                                                            option?.city?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                            option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                            option?.line2?.toLowerCase().includes(inputValue.toLowerCase())
                                                        );
                                                    }}
                                                    classes={{
                                                        paper: styles.autocompleteDropdown
                                                    }}
                                                    onInputChange={(event, newInputValue) => {
                                                        field.onChange(newInputValue);
                                                        setIsCreatePoDirty(true);
                                                        // Control when dropdown opens based on input length
                                                        if (newInputValue.length > 0) {
                                                            setAutocompleteOpenCity(true);
                                                        } else {
                                                            setAutocompleteOpenCity(false);
                                                        }
                                                    }}
                                                    onChange={(event, selectedOption) => {
                                                        if (selectedOption && typeof selectedOption === 'object') {
                                                            // Auto-populate all shipping details fields
                                                            setValue('shipping_details.line1', selectedOption.line1);
                                                            setValue('shipping_details.line2', selectedOption.line2 || '');
                                                            setValue('shipping_details.city', selectedOption.city);
                                                            setValue('shipping_details.state_id', selectedOption.state_id);
                                                            setValue('shipping_details.zip', selectedOption.zip);
                                                            const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                                                            setStateDropDownValue(stateName);
                                                            setIsCreatePoDirty(true);
                                                            saveUserActivity();
                                                            saveBomHeaderDetails();
                                                        }
                                                    }}
                                                    renderInput={(params) => (
                                                        <TextField
                                                            {...params}
                                                            className={clsx(styles.addressInputs, styles.muiAutocompleteTextField)}
                                                            type='text'
                                                            placeholder='CITY'
                                                            onBlur={(e) => {
                                                                e.target.value = e.target.value.trim();
                                                                field.onBlur();
                                                                saveUserActivity();
                                                                saveBomHeaderDetails();
                                                            }}
                                                            onKeyDown={(e) => {
                                                                handleAutocompleteTabSelection(
                                                                    e,
                                                                    field,
                                                                    autocompleteOpenCity,
                                                                    ['city', 'line1', 'line2'],
                                                                    setAutocompleteOpenCity,
                                                                    'input[name="shipping_details.zip"]'
                                                                );
                                                            }}
                                                        />
                                                    )}
                                                    renderOption={(props, option) => (
                                                        <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                            <div>
                                                                {formatAddressDisplay(option)}
                                                            </div>
                                                        </li>
                                                    )}
                                                    freeSolo
                                                    clearOnEscape
                                                    noOptionsText=""
                                                />
                                            )}
                                        />
                                    </span>

                                    <span className={clsx(styles.addressInputsCol2, errors?.shipping_details?.state_id?.message && styles.errorInput,
                                        stateInputFocus && styles.selectShade)}>
                                        {stateInputFocus && <>
                                            <div className={styles.shape1}></div>
                                            <div className={styles.shape2}></div>
                                        </>

                                        }
                                        <StateDropDown
                                            states={states}
                                            setValue={setValue}
                                            stateDropDownValue={stateDropDownValue}
                                            setStateDropDownValue={setStateDropDownValue}
                                            setStateInputFocus={setStateInputFocus}
                                            stateInputFocus={stateInputFocus}
                                        />
                                    </span>
                                    <span className={styles.addressInputsCol3}>
                                        <Tooltip
                                            title={errors?.shipping_details?.zip?.message}
                                            arrow
                                            placement={"top-end"}
                                            disableInteractive
                                            TransitionComponent={Fade}
                                            TransitionProps={{ timeout: 200 }}
                                            classes={{
                                                tooltip: 'stateTooltip',
                                            }}
                                        >
                                            <div>
                                                <InputWrapper>
                                                    <CustomTextField className={clsx(styles.addressInputs, errors?.shipping_details?.zip?.message && styles.errorInput)} type='text'
                                                        register={register("shipping_details.zip")}
                                                        placeholder='ZIP'
                                                        onChange={(e) => {
                                                            register("shipping_details.zip").onChange(e);
                                                            const zipCode = e.target.value.replace(/\D/g, '');
                                                            setValue("shipping_details.zip", zipCode);
                                                        }}
                                                        maxLength={5}
                                                        onBlur={(e) => {
                                                            e.target.value = e.target.value.trim();
                                                            register("shipping_details.zip").onBlur(e);
                                                            saveUserActivity();
                                                            saveBomHeaderDetails();
                                                        }}
                                                        onKeyDown={(e) => {
                                                            if (e.key === 'Tab') {
                                                                if (!e.shiftKey) {
                                                                    e.stopPropagation();
                                                                    e.preventDefault();
                                                                    const nextElement = document.querySelector('[tabindex="18"]');
                                                                    if (nextElement instanceof HTMLElement) {
                                                                        nextElement.focus();
                                                                    }
                                                                    handleDeliveryInfoContainerClickAway()
                                                                }
                                                            }
                                                        }}
                                                        errorInput={errors?.shipping_details?.zip?.message}
                                                    />
                                                </InputWrapper>
                                            </div>
                                        </Tooltip>
                                    </span>

                                </span>
                            </span>
                            :
                            <span className={styles.deliverToLabel}>
                                {
                                    (watch('shipping_details.line1') || watch('shipping_details.line2') || (watch('shipping_details.city') || stateDropDownValue || watch('shipping_details.zip'))) ? (<>
                                        <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('shipping_details.line1') ? `${watch('shipping_details.line1')}` : ''}</p>
                                        <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('shipping_details.line2') ? `${watch('shipping_details.line2')}` : ''}</p>
                                        <span className={styles.lastAddressFiled}>
                                            <p className={clsx(styles.addressInputsCol1, styles.hideInputBackground)}>{watch('shipping_details.city') ? `${watch('shipping_details.city')}` : ''}</p>
                                            <p className={clsx(styles.addressInputsCol2, styles.hideInputBackground)}>{stateDropDownValue ? stateDropDownValue : ''}</p>
                                            <p className={clsx(styles.addressInputsCol3, styles.hideInputBackground)}>{watch('shipping_details.zip') ? `${watch('shipping_details.zip')}` : ''}</p>
                                        </span>
                                    </>) : (<span>DELIVER TO</span>)
                                }

                            </span>
                        }

                    </div>
                </ClickAwayListener>
                <div className={`${styles.uploadBillContainer} ${!orderInfoIsFilled ? styles.disabled : ''}`} tabIndex={orderInfoIsFilled ? 18 : -1}
                    id="upload-bill-of-material"
                    onFocus={() => {
                        scrollToTop();
                    }}

                    onKeyDown={(e) => {
                        if (e.key === 'Tab') {
                            if (!e.shiftKey) {
                                if (orderInfoIsFilled) {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    const descriptionInput = document.getElementById('combo-box-demo0');//document.querySelector('textarea[id="combo-box-demo"]');
                                    if (descriptionInput instanceof HTMLElement) {
                                        descriptionInput.focus();
                                    }
                                }
                            } else {
                                setOpenDeliveryToDialog(true);
                                setIsFocused(true)
                                setTimeout(() => {
                                    const descriptionInput = document.querySelector('input[name="shipping_details.line1"]');
                                    if (descriptionInput instanceof HTMLElement) {
                                        descriptionInput.focus();
                                    }
                                }, 100)
                            }
                        }
                        if (e.key === 'Enter' || e.key === ' ') {
                            handleUploadClick()
                        }
                    }}
                    onClick={handleUploadClick}
                >
                    <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileUpload}
                        accept=".pdf,.doc,.docx,.xlsx,.xls"
                        style={{ display: 'none' }}
                    />
                    <div className={styles.uploadIcon}>
                        <UploadBOMIcon className={styles.uploadIcon1} />
                        <UploadBOMIconHover className={styles.uploadIcon2} />
                    </div>
                    <span className={styles.uploadLabel}>
                        UPLOAD BILL<br />OF MATERIAL
                    </span>
                </div>
            </div>
        </div>
    )
})

export default CreatePoHeaderInfo