import clsx from 'clsx';
import React, { useEffect, useRef, useState } from 'react'
import BomTile from '../bomTile/BomTile';
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import { localStorageKeys } from 'src/renderer2/common';
import { useBomReviewStore } from '../BomReview/BomReviewStore';

const BomReviewTable = (
    {
        styles,
        createPoContainerRef,
        formInputGroupRef,
        orderInfoIsFilled,
        hidePoLineScroll,
        setHidePoLineScroll,
        filteredFields,
        addPoLineTableRef,
        bomUploadResult,
        register,
        fields,
        products,
        setValue,
        watch,
        errors,
        control,
        getValues,
        pricePerUnitChangeHandler,
        userPartData,
        sessionId,
        searchStringData,
        setSearchString,
        setLineSessionId,
        lineSessionId,
        setDisableBidBuyNow,
        setOpenDeliveryToDialog,
        scrollToTop,
        clearErrors,
        currentBomData,
        updateLineProductTag,
        validateSavedBomCreatePo,
        scrollPoHeaderToBottom,
        setFocusJobPoInput,
        scrollerRef
    }
) => {

    const {setScrollToBomLine, scrollToBomLine} = useRightWindowStore();
    const [lastModifiedBom, setLastModifiedBom] = useState<string | null>(null);
    const lastModifiedBomRef = useRef(null);
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const setViewIndex = useBomReviewStore((state) => state.setViewIndex);
    const viewIndex = useBomReviewStore((state) => state.viewIndex);
    let viewLineStatusIndex = 0;

    useEffect(() => {
        const scrollToElement = (table: HTMLElement, element: HTMLElement) => {
            const elementOffsetTop = element.offsetTop;
            table.scrollTo({ top: elementOffsetTop - 200, behavior: 'smooth' });
        };

        let _lastModifiedBom = null;
        if (currentBomData?.id) {
            _lastModifiedBom = getLastModifiedBom(currentBomData?.id);
            setLastModifiedBom(_lastModifiedBom?.lineId);
        }

        if (
            (_lastModifiedBom || lastModifiedBomRef?.current) &&
            currentBomData &&
            createPoContainerRef?.current &&
            addPoLineTableRef?.current
        ) {
            if (_lastModifiedBom !== "HEADER") {
                setTimeout(() => {
                    const container = createPoContainerRef.current;
                    const table = addPoLineTableRef.current;
                    const element = lastModifiedBomRef.current;
                    const localLastModifiedBom = document.getElementById(_lastModifiedBom?.lineId);
                    if (container) {
                        container.scrollTo({ top: 300, behavior: 'smooth' });
                    }

                    if (table && (element || localLastModifiedBom)) {
                        if (localLastModifiedBom) {
                            scrollToElement(table, localLastModifiedBom);
                            focusChildElement(localLastModifiedBom, _lastModifiedBom?.input);
                        } else {
                            scrollToElement(table, element);
                            focusChildElement(element, _lastModifiedBom?.input);
                        }
                    }

                }, 200)
            } else {
                setFocusJobPoInput(true);
            }
        }
    }, [currentBomData]);

    const getLastModifiedBom = (key: string): string | null => {
        try {
            const lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
            if (lastModifiedBom) {
                const lastModifiedBomData = JSON.parse(lastModifiedBom);
                if (key in lastModifiedBomData) {
                    return lastModifiedBomData[key];
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } catch (e) {
            console.warn('Error checking key in localStorage', e);
            return null;
        }
    };

    const focusChildElement = (parentElement: HTMLElement | any, elementToFocus: string = "description") => {
        if (elementToFocus === "qty") {
            const childElement = parentElement.querySelector('[id^="qty-input-"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else if (elementToFocus === "product_tag") {
            const childElement = parentElement.querySelector('[name^="cart_items."][name$=".product_tag"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else {
            const childElement = parentElement.querySelector('[id^="combo-box-demo"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        };
    }

    const scrollBomLineToSpecificIndex = (bomLineId: string) => {
        const scrollToElement = (table: HTMLElement, element: HTMLElement) => {
            const elementOffsetTop = element.offsetTop;
            table.scrollTo({ top: elementOffsetTop - 200 });
        };
        const table = addPoLineTableRef.current;
        const element = document.getElementById(bomLineId);

        if (table && element) {
            scrollToElement(table, element);
            focusChildElement(element);
        }
        setScrollToBomLine(null)
    }

    useEffect(() => {
        if (scrollToBomLine) {
            handleScrollToBomLine(scrollToBomLine)
        }
    }, [scrollToBomLine])


    const handleScrollToBomLine = (bomLineId: string) => {
        const isBomLineIdPresent = filteredFields.some((item: any) => item.bom_line_id === bomLineId);
        if (!isBomLineIdPresent) {
            const index = bomUploadResult.findIndex((item: any) => item.bom_line_id === bomLineId);
            if (index !== -1) {
                // setEndIndex((_startIndex + 10) >= 0 ? (_startIndex + 20) : 0);
                setTimeout(() => {
                    scrollPoHeaderToBottom()
                    scrollBomLineToSpecificIndex(bomLineId);
                }, 500)
            }
        } else {
            scrollBomLineToSpecificIndex(bomLineId);
        }
    }
    
    const handleLineItemScroll = () => {
        // Skip if the scroll is from dragging the thumb
        if (!addPoLineTableRef.current) return;
        const { scrollTop } = addPoLineTableRef.current;
        if(scrollerRef.current) scrollerRef.current.updateScrollPosition(204 + scrollTop);
        // handleViewIndexChange();
    }
    
    // const handleViewIndexChange = () => {
    //     const container = addPoLineTableRef.current;
    //     if (container) {
    //         const firstRow = container.querySelector('tbody tr:first-child');
    //         if (firstRow) {
    //             const { scrollTop } = container;
    //             const rect = firstRow.getBoundingClientRect();

    //             // Get computed styles to access margins
    //             const computedStyle = window.getComputedStyle(firstRow);

    //             // Calculate total height including margins
    //             const marginTop = parseInt(computedStyle.marginTop, 10);
    //             const marginBottom = parseInt(computedStyle.marginBottom, 10);

    //             // Total height = height + margin-top + margin-bottom
    //             const firstRowHeight = rect.height + marginTop + marginBottom;
    //             const index = Math.floor(scrollTop / firstRowHeight);
                
    //             // Clear existing timeout
    //             if (debounceTimeoutRef.current) {
    //                 clearTimeout(debounceTimeoutRef.current);
    //             }

    //             // Set new timeout to debounce the setViewIndex call
    //             debounceTimeoutRef.current = setTimeout(() => {
    //                 setViewIndex(index - (index%5));
    //             }, 400);
    //         } else {
    //             console.warn('First row not found');
    //         }
    //     }
    // };

    const openAddLineTab = () => {
        if (!addPoLineTableRef.current) return;
        const container = addPoLineTableRef.current;
        if (container.scrollTop > 0 && orderInfoIsFilled && createPoContainerRef.current) {
            createPoContainerRef.current.scrollTo({
                top: createPoContainerRef.current.scrollHeight,
                behavior: 'smooth'
            });
            container.style.overflowY = 'auto';
            const formInputHeight = formInputGroupRef.current?.clientHeight;
            setTimeout(() => {
                if(scrollerRef.current) scrollerRef.current.updateScrollPosition(container.scrollTop + formInputHeight);
            }, 400)
        }
    }

    return (
        <div style={{ overflowY: (!hidePoLineScroll && orderInfoIsFilled) ? 'auto' : 'hidden' }} className={clsx(styles.uploadBOMLineTable, (filteredFields?.length === 3) && styles.uploadBOMLineTableMinHeight)} onScroll={handleLineItemScroll} ref={addPoLineTableRef} onClick={() => { openAddLineTab() }}>
            <table >
                <thead>
                    <tr>
                        <th><span>LN</span></th>
                        <th><span>DESCRIPTION</span></th>
                        <th><span>QTY</span></th>
                        <th colSpan={2}><span>LINE STATUS</span></th>
                    </tr>
                </thead>
                <tbody>
                    {
                        filteredFields.map((item: any, _index: number) => {
                            const index = item.lineStatusIndex;
                            viewLineStatusIndex++;
                            // Watch the specific values for optimization
                            const lineStatus = watch(`cart_items.${index}.lineStatus`);
                            const domesticMaterialOnly = watch(`cart_items.${index}.domesticMaterialOnly`);
                            const descriptionObj = watch(`cart_items.${index}.descriptionObj`);
                            const productTag = watch(`cart_items.${index}.product_tag`);
                            const qty = watch(`cart_items.${index}.qty`);
                            const qtyUnit = watch(`cart_items.${index}.qty_unit`);
                            
                            return (
                                <React.Fragment key={_index}>
                                    {
                                        ((index) < bomUploadResult?.length) && <BomTile
                                            index={index}
                                            register={register}
                                            fields={fields}
                                            products={products}
                                            setValue={setValue}
                                            watch={watch}
                                            errors={errors}
                                            control={control}
                                            getValues={getValues}
                                            pricePerUnitChangeHandler={pricePerUnitChangeHandler}
                                            userPartData={userPartData}
                                            sessionId={sessionId}
                                            searchStringData={searchStringData}
                                            setSearchString={setSearchString}
                                            setLineSessionId={setLineSessionId}
                                            lineSessionId={lineSessionId}
                                            orderInfoIsFilled={orderInfoIsFilled}
                                            setDisableBidBuyNow={setDisableBidBuyNow}
                                            openAddLineTab={openAddLineTab}
                                            setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                            hidePoLineScroll={hidePoLineScroll}
                                            setHidePoLineScroll={setHidePoLineScroll}
                                            scrollToTop={scrollToTop}
                                            bomTileDefaultData={bomUploadResult[index] ?? []}
                                            clearErrors={clearErrors}
                                            lastModifiedBomRef={lastModifiedBomRef}
                                            lastModifiedBom={lastModifiedBom}
                                            currentBomData={currentBomData}
                                            updateLineProductTag={updateLineProductTag}
                                            filterFieldsLength={filteredFields.length}
                                            filteredItemIndex={_index}
                                            validateSavedBomCreatePo={validateSavedBomCreatePo}
                                            // Optimized props for BomTile rerendering
                                            lineStatus={lineStatus}
                                            domesticMaterialOnly={domesticMaterialOnly}
                                            descriptionObj={descriptionObj}
                                            productTag={productTag}
                                            qty={qty}
                                            qtyUnit={qtyUnit}
                                            viewLineStatusIndex={viewLineStatusIndex}
                                        />

                                    }
                                </React.Fragment>
                            )
                        })
                    }
                </tbody>
            </table>
        </div>
    )
}

export default BomReviewTable