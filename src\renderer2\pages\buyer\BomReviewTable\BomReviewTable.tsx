import { useEffect, useRef, useState } from 'react'
import VirtualizedBomTable from './VirtualizedBomTable';
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import { localStorageKeys } from 'src/renderer2/common';
import { useBomReviewStore } from '../BomReview/BomReviewStore';

const BomReviewTable = (
    {
        styles,
        createPoContainerRef,
        formInputGroupRef,
        orderInfoIsFilled,
        hidePoLineScroll,
        setHidePoLineScroll,
        filteredFields,
        addPoLineTableRef,
        bomUploadResult,
        register,
        fields,
        products,
        setValue,
        watch,
        errors,
        control,
        getValues,
        pricePerUnitChangeHandler,
        userPartData,
        sessionId,
        searchStringData,
        setSearchString,
        setLineSessionId,
        lineSessionId,
        setDisableBidBuyNow,
        setOpenDeliveryToDialog,
        scrollToTop,
        clearErrors,
        currentBomData,
        updateLineProductTag,
        validateSavedBomCreatePo,
        scrollPoHeaderToBottom,
        setFocusJobPoInput,
        scrollerRef
    }
) => {

    const {setScrollToBomLine, scrollToBomLine} = useRightWindowStore();
    const [lastModifiedBom, setLastModifiedBom] = useState<string | null>(null);
    const lastModifiedBomRef = useRef(null);
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const setViewIndex = useBomReviewStore((state) => state.setViewIndex);
    const viewIndex = useBomReviewStore((state) => state.viewIndex);

    useEffect(() => {
        const scrollToElement = (table: HTMLElement, element: HTMLElement) => {
            const elementOffsetTop = element.offsetTop;
            table.scrollTo({ top: elementOffsetTop - 200, behavior: 'smooth' });
        };

        let _lastModifiedBom = null;
        if (currentBomData?.id) {
            _lastModifiedBom = getLastModifiedBom(currentBomData?.id);
            setLastModifiedBom(_lastModifiedBom?.lineId);
        }

        if (
            (_lastModifiedBom || lastModifiedBomRef?.current) &&
            currentBomData &&
            createPoContainerRef?.current &&
            addPoLineTableRef?.current
        ) {
            if (_lastModifiedBom !== "HEADER") {
                setTimeout(() => {
                    const container = createPoContainerRef.current;
                    const table = addPoLineTableRef.current;
                    const element = lastModifiedBomRef.current;
                    const localLastModifiedBom = document.getElementById(_lastModifiedBom?.lineId);
                    if (container) {
                        container.scrollTo({ top: 300, behavior: 'smooth' });
                    }

                    if (table && (element || localLastModifiedBom)) {
                        if (localLastModifiedBom) {
                            scrollToElement(table, localLastModifiedBom);
                            focusChildElement(localLastModifiedBom, _lastModifiedBom?.input);
                        } else {
                            scrollToElement(table, element);
                            focusChildElement(element, _lastModifiedBom?.input);
                        }
                    }

                }, 200)
            } else {
                setFocusJobPoInput(true);
            }
        }
    }, [currentBomData]);

    const getLastModifiedBom = (key: string): string | null => {
        try {
            const lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
            if (lastModifiedBom) {
                const lastModifiedBomData = JSON.parse(lastModifiedBom);
                if (key in lastModifiedBomData) {
                    return lastModifiedBomData[key];
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } catch (e) {
            console.warn('Error checking key in localStorage', e);
            return null;
        }
    };

    const focusChildElement = (parentElement: HTMLElement | any, elementToFocus: string = "description") => {
        if (elementToFocus === "qty") {
            const childElement = parentElement.querySelector('[id^="qty-input-"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else if (elementToFocus === "product_tag") {
            const childElement = parentElement.querySelector('[name^="cart_items."][name$=".product_tag"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else {
            const childElement = parentElement.querySelector('[id^="combo-box-demo"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        };
    }

    const scrollBomLineToSpecificIndex = (bomLineId: string) => {
        const scrollToElement = (table: HTMLElement, element: HTMLElement) => {
            const elementOffsetTop = element.offsetTop;
            table.scrollTo({ top: elementOffsetTop - 200 });
        };
        const table = addPoLineTableRef.current;
        const element = document.getElementById(bomLineId);

        if (table && element) {
            scrollToElement(table, element);
            focusChildElement(element);
        }
        setScrollToBomLine(null)
    }

    useEffect(() => {
        if (scrollToBomLine) {
            handleScrollToBomLine(scrollToBomLine)
        }
    }, [scrollToBomLine])


    const handleScrollToBomLine = (bomLineId: string) => {
        const isBomLineIdPresent = filteredFields.some((item: any) => item.bom_line_id === bomLineId);
        if (!isBomLineIdPresent) {
            const index = bomUploadResult.findIndex((item: any) => item.bom_line_id === bomLineId);
            if (index !== -1) {
                // setEndIndex((_startIndex + 10) >= 0 ? (_startIndex + 20) : 0);
                setTimeout(() => {
                    scrollPoHeaderToBottom()
                    scrollBomLineToSpecificIndex(bomLineId);
                }, 500)
            }
        } else {
            scrollBomLineToSpecificIndex(bomLineId);
        }
    }
    


    return (
        <VirtualizedBomTable
            styles={styles}
            createPoContainerRef={createPoContainerRef}
            formInputGroupRef={formInputGroupRef}
            orderInfoIsFilled={orderInfoIsFilled}
            hidePoLineScroll={hidePoLineScroll}
            setHidePoLineScroll={setHidePoLineScroll}
            filteredFields={filteredFields}
            addPoLineTableRef={addPoLineTableRef}
            bomUploadResult={bomUploadResult}
            register={register}
            fields={fields}
            products={products}
            setValue={setValue}
            watch={watch}
            errors={errors}
            control={control}
            getValues={getValues}
            pricePerUnitChangeHandler={pricePerUnitChangeHandler}
            userPartData={userPartData}
            sessionId={sessionId}
            searchStringData={searchStringData}
            setSearchString={setSearchString}
            setLineSessionId={setLineSessionId}
            lineSessionId={lineSessionId}
            setDisableBidBuyNow={setDisableBidBuyNow}
            setOpenDeliveryToDialog={setOpenDeliveryToDialog}
            scrollToTop={scrollToTop}
            clearErrors={clearErrors}
            currentBomData={currentBomData}
            updateLineProductTag={updateLineProductTag}
            validateSavedBomCreatePo={validateSavedBomCreatePo}
            scrollPoHeaderToBottom={scrollPoHeaderToBottom}
            setFocusJobPoInput={setFocusJobPoInput}
            scrollerRef={scrollerRef}
            lastModifiedBomRef={lastModifiedBomRef}
            lastModifiedBom={lastModifiedBom}
        />
    )
}

export default BomReviewTable