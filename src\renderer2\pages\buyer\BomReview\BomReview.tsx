import { Dialog } from '@mui/material';
import React, { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { useLocation } from 'react-router';
import styles from './BomReview.module.scss';
import clsx from 'clsx';
import {  useFieldArray } from 'react-hook-form';
import { useDebouncedValue } from '@mantine/hooks';
import dayjs from 'dayjs';
import useGetUserPartData from '../../../hooks/useGetUserPartData';
import { useQueryClient } from '@tanstack/react-query';
import { v4 as uuidv4 } from 'uuid';
import { useGlobalStore, useSaveUserActivity, getFloatRemainder, getValUsingUnitKey, useBuyerSettingStore, useCreatePoStore, uploadBomConst, getSocketConnection, useBuyerCheckOutNode, orderIncrementPrefix, priceUnits, newPricingPrefix} from '@bryzos/giss-ui-library';
import { calculateBuyerTotalOrderWeightForGear } from '../../../utility/pdfUtils';
import SearchHeader from '../../SearchHeader';
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import { routes, creditLimitStatus, bomLineStatusCountObjDefault, localStorageKeys } from 'src/renderer2/common';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import UploadBomSummary from 'src/renderer2/component/UploadBomSummary/UploadBomSummary';
import BomProcessingWindow from 'src/renderer2/component/BomProcessingWindow/BomProcessingWindow';
import useGetAvailableCreditLimit from 'src/renderer2/hooks/useGetAvailableCreditLimit';
import useSaveBomHeaderDetails from 'src/renderer2/hooks/useSaveBomHeaderDetails';
import usePostDraftPo from 'src/renderer2/hooks/usePostDraftPo';
import { useBomPdfExtractorStore } from '../BomPdfExtractor/BomPdfExtractorStore';
import CreatePoHeaderInfo from '../CreatePoHeaderInfo/CreatePoHeaderInfo';
import { useGenericForm } from 'src/renderer2/hooks/useGenericForm';
import { bomReviewSchema } from 'src/renderer2/models/bomReview.model';
import BomReviewTable from '../BomReviewTable/BomReviewTable';
import { useBomReviewStore } from './BomReviewStore';
import Scroller, { ScrollerRef } from 'src/renderer2/component/Scroller';

const BomReviewComponent = () => {
    const location = useLocation();
    const {
        control,
        register,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        reset,
        trigger,
        errors
    } = useGenericForm(bomReviewSchema, {
        defaultValues: {
            'cart_items':  [],
            'freight_term': "Delivered"
        }
    });

    const { setShowLoader, setCreatePoSessionId, setBackNavigation, referenceData, productData, productMapping } = useGlobalStore();
    const { bomProductMappingSocketData, setBomProductMappingSocketData, setIsCreatePOModule, setCreatePoData, createPoData, bomProductMappingDataFromSavedBom, createPoDataFromSavedBom, setCreatePoDataFromSavedBom, setBomProductMappingDataFromSavedBom, setBomDataIdToRefresh, bomSummaryViewFilter, setBomSummaryViewFilter, setUploadBomInitialData, uploadBomInitialData } = useCreatePoStore();

    const { bomData } = useBomPdfExtractorStore();
    const { setLoadComponent, setShowVideo, setProps, bomProcessingWindowProps, setShowBomProcessing, setBOMLineStatusCountObj,  setScrollToBomLine } = useRightWindowStore();

    const [products, setProducts] = useState([]);

    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const { data: userPartData } = useGetUserPartData();
    const mutateSaveBomHeaderDetails = useSaveBomHeaderDetails();
    // const setScrollPosition = useBomReviewStore((state) => state.setScrollPosition);
    const setViewIndex = useBomReviewStore((state) => state.setViewIndex);
    const scrollerRef = useRef<ScrollerRef>(null);

    const { fields, append } = useFieldArray({
        control,
        name: "cart_items"
    });
    
    const [sessionId, setSessionId] = useState('');
    const [lineSessionId, setLineSessionId] = useState('');
    const [searchStringData, setSearchString] = useState('');
    // const getBuyingPreference = ueGetBuyingPreference();
    const { buyerSetting } = useBuyerSettingStore();
    // const saveProductSearch = useSaveProductSearchAnaytic();
    const [openDeliveryToDialog, setOpenDeliveryToDialog] = useState(false);
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const createPoContainerRef = useRef(null);
    const headerContainerRef = useRef<HTMLDivElement>(null);
    // const [isCartValid, setIsCartValid] = useState(true);
    // const [isAllCartDataLoaded, setIsAllCartDataLoaded] = useState(false);
    // const [pricingBrackets, setPricingBrackets] = useState([]);
    const [disableBidBuyNow, setDisableBidBuyNow] = useState(false);
    const addPoLineTableRef = useRef(null);
    const [hidePoLineScroll, setHidePoLineScroll] = useState(true);
    const [maxScrollHeight, setMaxScrollHeight] = useState(800);
    const formInputGroupRef = useRef(null);
    const [bomUploadResult, setBomUploadResult] = useState([]);
    const getAvailableCreditLimit = useGetAvailableCreditLimit();
    const [isSavedBom, setIsSavedBom] = useState(location.pathname === routes.savedBom);
    const [initialData, setInitialData] = useState(location.pathname !== routes.savedBom ? createPoData : null);
    const [cameFromSavedBom, setCameFromSavedBom] = useState(false);
    const [isHeaderDetailsConfirmed, setIsHeaderDetailsConfirmed] = useState(false);
    const [filteredFields, setFilteredFields] = useState<any>([]);
    const [currentBomData, setCurrentBomData] = useState(location.pathname === routes.savedBom ? bomProductMappingSocketData : null);
    // Track the cart items length separately
    const cartItemsLength = watch('cart_items')?.length || 0;
    const socket = getSocketConnection();
    const HeaderDetailsConfirmedRef = useRef(null);

    const createPoHeaderInfoRef = useRef<any>(null);

    const {orderInfoIsFilled, initializePoHeaderForm, watch: poHeaderFormWatch, getHeaderFormData} = createPoHeaderInfoRef.current ?? {};
    // const orderInfoIsFilled = createPoHeaderInfoRef.current?.orderInfoIsFilled ?? false; 
    const [focusJobPoInput, setFocusJobPoInput] = useState(false);

    useEffect(() => {
        setIsSavedBom(location.pathname === routes.savedBom)
        return (() => {
            if(location.pathname !== routes.savedBom && getHeaderFormData){
                handleLeavePageAction();
            }
        })
    }, [getHeaderFormData])

    // Replace the existing handleLeavePageAction function
    const handleLeavePageAction = () => {
        const { bomDataIdToRefresh } = useCreatePoStore.getState();
        if (bomDataIdToRefresh) {
            socket?.emit('getBomDataById', { bom_upload_id: bomDataIdToRefresh });
        }

        saveBomHeaderDetails();
        setBomUploadResult([]);
        if (bomProcessingWindowProps?.isProcessing !== true) {
            setBomProductMappingSocketData(null);//do this conditionally
            setIsCreatePOModule(true);
            setCreatePoDataFromSavedBom(null);
            setBomProductMappingDataFromSavedBom(null);
        }
        setScrollToBomLine(null)
        setBomDataIdToRefresh(null);
    }

    // Use the tracked length in useEffect
    useEffect(() => {
        const formInputGroup = formInputGroupRef.current;
        const addLineContainer = addPoLineTableRef.current;
        if (!!formInputGroup && !!addLineContainer) {
            setTimeout(() => {
                setMaxScrollHeight((formInputGroup?.scrollHeight + addLineContainer?.scrollHeight) ?? 800);
            }, 200)
        }
    }, [cartItemsLength, filteredFields.length]); // Now depends on the tracked length

    const checkAtLeastOneApproved = useMemo(() => 
        watch('cart_items')?.some((item: any) => item.lineStatus === 'APPROVED'),
        [watch('cart_items')]
    );
    
    const isValidBomForm = useMemo(() => 
        watch('cart_items')?.some((item: any, index: number) => {
            if (item.lineStatus === 'SKIPPED' || item.lineStatus === 'DELETED') {
                return false;
            } else if (item.lineStatus === 'APPROVED') {
                return !(item.qty?.length > 0 && item.descriptionObj && Object.keys(item.descriptionObj).length > 0)
            }
            return (
                item.lineStatus === 'PENDING'
            );
        }),
        [watch('cart_items')]
    );
    //*IMP*
    const disableReviewCompleteButton = !orderInfoIsFilled || !checkAtLeastOneApproved || isValidBomForm
    // const disableReviewCompleteButton = true || !checkAtLeastOneApproved || isValidBomForm

    useEffect(() => {
        const uploadBomSummaryProps = { getValues, disableReviewCompleteButton, bomProductMappingSocketData: currentBomData, watch, getHeaderFormData }
        setProps(uploadBomSummaryProps);
    }, [
        disableReviewCompleteButton,
        currentBomData,
        getHeaderFormData,
        watch('cart_items')
    ]);

    useEffect(() => {
        const component = <UploadBomSummary />;
        setLoadComponent(component);
    }, []);

    useEffect(() => {
        if (bomProcessingWindowProps?.isProcessing === true) {
            setShowVideo(false);
            setShowBomProcessing(true);
        } else {
            setShowVideo(true);
            setShowBomProcessing(false);
        }
        reset();
        // poHeaderForm.reset();
        // setIsAllCartDataLoaded(false);
        setBomUploadResult([])
        return (() => {
            setLoadComponent(null)
            setShowBomProcessing(false);
            if (bomProcessingWindowProps?.isProcessing === true) {
                setLoadComponent(
                    <div >
                        <BomProcessingWindow
                        // isCompleted={processingCompleted}
                        // gameScoreData={getGameScore}
                        />
                    </div>
                );
            }
            setBackNavigation(-1)
        })
    }, []);

    useEffect(() => {
        console.log("-------initializePoHeaderForm----------", initializePoHeaderForm);
        console.log("-------initialData----------", initialData);
        if(initializePoHeaderForm){
            _init();
        }
    }, [initializePoHeaderForm]);

    const _init = () => {
        if (productData && referenceData) {
            setProducts(productData)
            // setPricingBrackets(referenceData?.ref_weight_price_brackets);
            // initializeCreatePOData();
            const _intialData = getInitialData();
            console.log("-------_intialData----------", _intialData);
            if(_intialData)
            initializePoHeaderForm(_intialData);
        }
        const sessionId = uuidv4();
        setSessionId(sessionId);
        setCreatePoSessionId(sessionId)
        // if (isCreatePOModule) {
        //     const payload = {
        //         "data": {
        //             "session_id": sessionId
        //         }
        //     }
        //     logUserActivity.mutateAsync({ url: import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload }).catch(err => console.error(err));
        // }
    }

    const initializeBomTileData = (index: number) => { 
        const selectedProduct = watch(`cart_items.${index}.selected_products`);
        if (selectedProduct?.length > 0) {
          const product = productMapping[selectedProduct[0]];
          if(product){
            setValue(`cart_items.${index}.descriptionObj`, product);
            setValue(`cart_items.${index}.qty_um`, product.QUM_Dropdown_Options?.split(","));
            
            // Only set qty_unit if 'Ea' is not in QUM options
            const qumOptions = product.QUM_Dropdown_Options?.split(",");
            if (!qumOptions.includes(watch(`cart_items.${index}.qty_unit`))) {
                setValue(`cart_items.${index}.qty_unit`, qumOptions[0]);
            }
          }
        }
    }

    useEffect(() => {
        if (currentBomData?.result?.length > 0) {
            const cartItem = [];
            const statusCountObj = { ...bomLineStatusCountObjDefault };
            for (let i = 0; i < currentBomData.result.length; i++) {
                const productObj = {
                    lineStatusIndex: i,
                    bom_line_id: currentBomData.result[i].id || i,
                    lineStatus: currentBomData.result[i].status,
                    originalStatus: currentBomData.result[i].original_line_status || currentBomData.result[i].status,
                    confidence: currentBomData.result[i].confidence,
                    product_tag: currentBomData.result[i].product_tag,
                    description: currentBomData.result[i].description,
                    specification: currentBomData.result[i].specification,
                    search_string: currentBomData.result[i].search_string,
                    matched_products: currentBomData.result[i].matched_products,
                    selected_products: currentBomData.result[i].selected_products,
                    current_page: currentBomData.result[i].current_page,
                    total_pages: currentBomData.result[i].total_pages,
                    product_index: currentBomData.result[i].product_index,
                    grade: currentBomData.result[i].grade,
                    qty: currentBomData.result[i].qty?.replace(/[\$,]/g, '') || currentBomData.result[i].qty,
                    qty_unit: currentBomData.result[i].qty_unit?.toLowerCase(),
                    length: currentBomData.result[i].length,
                    weight_per_quantity: currentBomData.result[i].weight_per_quantity,
                    matched_product_count: currentBomData.result[i].matched_product_count,
                    last_updated_product: currentBomData.result[i]?.last_updated_product ?? 0,
                    domesticMaterialOnly: currentBomData.result[i]?.domestic_material_only || false
                }
                if (productObj.lineStatus === uploadBomConst.lineItemStatus.approved) {
                    statusCountObj[uploadBomConst.lineItemStatus.approved]++;
                } else if (productObj.lineStatus === uploadBomConst.lineItemStatus.pending) {
                    statusCountObj[uploadBomConst.lineItemStatus.pending]++;
                } else if (productObj.lineStatus === uploadBomConst.lineItemStatus.skipped) {
                    statusCountObj[uploadBomConst.lineItemStatus.skipped]++;
                } else if (productObj.lineStatus === uploadBomConst.lineItemStatus.deleted) {
                    statusCountObj[uploadBomConst.lineItemStatus.deleted]++;
                }
                cartItem[i] = productObj;
            }
            setBomUploadResult(cartItem);
            setBOMLineStatusCountObj(statusCountObj);
        }
    }, [currentBomData])

    useEffect(() => {
        if (location.pathname !== routes.savedBom) {
            if (bomProductMappingSocketData) {
                setCurrentBomData(bomProductMappingSocketData);
            } else {
                setCurrentBomData(null);
            }
        }
    }, [bomProductMappingSocketData])

    useEffect(() => {
        if (location.pathname !== routes.savedBom) {
            if (bomData) {
                setCurrentBomData(bomData);
            } else {
                setCurrentBomData(null);
            }
        }
    }, [bomData])

    useEffect(() => {
        if (bomUploadResult?.length > 0) {
            setValue('cart_items', []);
            for (let i = 0; i < bomUploadResult.length; i++) {
                setDisableBidBuyNow(true);
                append({ ...bomUploadResult[i] })
                initializeBomTileData(i)
                validateSavedBomCreatePo(i)
            }
        }
    }, [bomUploadResult])

    useEffect(() => {
        if (watch('cart_items')?.length > 0) {
            handleFilterFieldsData();
        }
    }, [watch('cart_items')])

    useEffect(() => {
        setProducts(productData);
    }, [productData]);


    useEffect(() => {
        const initializeData = async () => {
            if (initialData && location.pathname === routes.savedBom) {
                await initializeCreatePOData();
                setShowLoader(false);
            }
        }
        initializeData();
    }, [initialData, location.pathname])

    useEffect(() => {
        const initializeData = async () => {
            if (isHeaderDetailsConfirmed) {
                await initializeCreatePOData();
            }
        };

        initializeData();
    }, [isHeaderDetailsConfirmed])

    useEffect(() => {
        if (location.pathname === routes.savedBom && (createPoDataFromSavedBom || bomProductMappingDataFromSavedBom)) {
            reset();
            setViewIndex(0);
            setBomSummaryViewFilter('all')
            setInitialData(null);
            setBomProductMappingSocketData(null);
            setScrollToBomLine(null)
            if (bomProductMappingDataFromSavedBom) {
                setInitialData({ ...bomProductMappingDataFromSavedBom });
                setCurrentBomData({ ...bomProductMappingDataFromSavedBom });
            } else if (createPoDataFromSavedBom) {
                handleCreatePoDataFromSavedBom();
            }
        }
    }, [location.pathname, createPoDataFromSavedBom, bomProductMappingDataFromSavedBom])

    useEffect(() => {
        const handleScrollOutsideDiv = (event) => {
            setIsCalendarOpen(false)
        };
        createPoContainerRef?.current?.addEventListener('scroll', handleScrollOutsideDiv);
        return () => {
            createPoContainerRef?.current?.removeEventListener('scroll', handleScrollOutsideDiv);
        };
    }, []);

    
    useEffect(() => {
        if (fields?.length > 0) {
            handleFilterFieldsData();
        }
    }, [bomSummaryViewFilter, bomProductMappingDataFromSavedBom, fields, watch('cart_items')])

    const handleFilterFieldsData = () => {
        const filteredFieldsData = fields.filter((item: any, index: number) => {
            const lineStatus = watch(`cart_items.${index}`)?.lineStatus
            const atLeastOneValid = watch('cart_items').some((item: any, index: number) => {
                const lineStatus = watch(`cart_items.${index}`)?.lineStatus;
                return (
                    bomSummaryViewFilter === 'all' ||
                    (bomSummaryViewFilter === 'red' && lineStatus === uploadBomConst.lineItemStatus.pending) ||
                    (bomSummaryViewFilter === 'green' && lineStatus !== uploadBomConst.lineItemStatus.pending)
                );
            });
            const checkValidation = ((bomSummaryViewFilter === 'all' || !atLeastOneValid) || (bomSummaryViewFilter === 'red' && lineStatus === uploadBomConst.lineItemStatus.pending) || (bomSummaryViewFilter === 'green' && lineStatus !== uploadBomConst.lineItemStatus.pending))
            return checkValidation;
        })
        setFilteredFields(filteredFieldsData);
    }

    const handleCreatePoDataFromSavedBom = () => {
        const _createPoDataFromSavedBom = { ...createPoDataFromSavedBom };
        const cartItem = _createPoDataFromSavedBom?.result?.map((item, i) => ({
            bom_line_id: item.id || i,
            lineStatus: item.status,
            originalStatus: item.original_line_status || item.status,
            confidence: item.confidence,
            product_tag: item.product_tag,
            description: item.description,
            specification: item.specification,
            search_string: item.search_string,
            matched_products: item.matched_products,
            selected_products: item.selected_products,
            current_page: item.current_page,
            total_pages: item.total_pages,
            product_index: item.product_index,
            grade: item.grade,
            qty: item.qty,
            qty_unit: item.qty_unit,
            length: item.length,
            weight_per_quantity: item.weight_per_quantity,
            matched_product_count: item.matched_product_count,
            price_unit: item.price_unit,
            price: item.price_per_unit,
            line_weight: item.line_weight ?? '0.00',
            extended: item?.buyer_line_total ? parseFloat(parseFloat(item.buyer_line_total).toFixed(2)) : 0,
            domesticMaterialOnly: item?.domestic_material_only || false,
            product_id: item.product_id,
            draft_line_id: item.id
        }));

        if (cartItem) {
            cartItem.forEach((item, index) => {
                if (item?.product_id) {
                    const product = productMapping[item.product_id];
                    if (product) {
                        item.descriptionObj = product;
                    }
                } else if (item?.selected_products?.length) {
                    const selectedProducts = item.selected_products;
                    const hasSelectedProducts = selectedProducts.length > 0;
                    if (hasSelectedProducts) {
                        const product = productMapping[selectedProducts[0]];
                        // Directly set the values on the cartItem object
                        item.descriptionObj = product;
                    }
                }
            });
        }
        setValue('cart_items', cartItem);
        setInitialData({ ...createPoDataFromSavedBom, cart_items: cartItem });
    }

    const getInitialData = () => {
        if (location.pathname === routes.bomUploadReview && location.state?.from === 'bomPdfExtractor') {
            return uploadBomInitialData;
        }
        return initialData;
    }

    const initializeCreatePOData = async () => {
        try {
            const _intialData = getInitialData();
            let buyingPreference = { ...buyerSetting };
            await initializeBuyerPaymentPreferences(buyingPreference)
            // const  res = await getBuyingPreference.mutateAsync()
            
            if (_intialData) {
                setValue('id', _intialData?.id ?? '');
                if (_intialData?.cameFromSavedBom && !isHeaderDetailsConfirmed) {
                    handleHeaderDetailsFill()
                } else {
                    if (!isHeaderDetailsConfirmed) {
                        initializePoHeaderForm(_intialData);
                    }
                    if (_intialData?.cart_items?.length > 0) {
                        setValue('cart_items', _intialData.cart_items);
                        setValue('seller_price', _intialData?.seller_price ?? '0');
                        setValue('price', _intialData?.price ?? '0');


                        if (_intialData?.cart_items?.length > 0 && !(isSavedBom && createPoDataFromSavedBom)) {
                            _intialData?.cart_items?.forEach((product: any, index: number) => {
                                if (product?.descriptionObj && Object.keys(product.descriptionObj).length > 0) {
                                    resetQtyAndPricePerUnitFields(index, product.descriptionObj)
                                    setDisableBidBuyNow(true)
                                };
                                // updateValue(index)
                            });
                        }
                    }
                    setValue('payment_method', _intialData?.payment_method ?? '');
                    setValue('sales_tax', _intialData?.sales_tax ?? 0);
                    if (_intialData?.bom_id) {
                        setValue('bom_id', _intialData?.bom_id ?? '');
                    }
                    setValue('is_draft_po', _intialData?.is_draft_po ?? false);
                    if (isSavedBom && createPoDataFromSavedBom) {
                        setValue('price', _intialData?.material_total)
                        setValue('totalPurchase', _intialData?.total_purchase)
                        setValue('depositAmount', _intialData?.deposit)
                        setValue('subscriptionAmount', _intialData?.subscription)
                        setValue('totalWeight', parseInt(_intialData?.total_weight) ?? 0);
                    } else {
                        setValue('totalWeight', calculateBuyerTotalOrderWeightForGear(watch('cart_items')));
                        // calculateMaterialTotalPrice();
                    }
                }
            } else {
                // setValue('internal_po_number', 'test');
                // setValue('order_type', 'BUY')
                // setValue('shipping_details.line1', buyingPreference.delivery_address_line1)
                // setValue('shipping_details.city', buyingPreference.delivery_address_city)
                // setValue('shipping_details.state_id', buyingPreference.delivery_address_state_id)
                // setStateDropDownValue(referenceData?.ref_states?.find(state => state.id === buyingPreference?.delivery_address_state_id)?.code || '');
                // setValue('shipping_details.zip', buyingPreference.delivery_address_zip)
                if (buyingPreference.default_payment_method === 'ACH_CREDIT') {
                    // setValue('payment_method', 'ach_credit')
                    setValue('depositAmount', 0);
                } else if (buyingPreference.default_payment_method === 'BUY_NOW_PAY_LATER') {
                    // setValue('payment_method', 'bryzos_pay')
                }
                setValue('sales_tax', 0);
            }
            setShowLoader(false);
            // setIsAllCartDataLoaded(true);
            if (buyingPreference?.bnpl_settings?.requested_credit_limit) {
                setValue('requestedCreditLimit', buyingPreference?.bnpl_settings?.requested_credit_limit)
            }
        } catch (err) {
            setOpenErrorDialog(true);
            setErrorMessage("Something went wrong. Please try again in sometime");
            setShowLoader(false);
            console.error(err)

        } finally {
            setCreatePoData(null);
            if (location.pathname !== routes.bomUploadReview) {
                setUploadBomInitialData(null);
            }
        }
    }

    const handleHeaderDetailsFill = () => {
        setCameFromSavedBom(true);
        initializePoHeaderForm(initialData);
    }

    const scrollPoHeaderToBottom = useCallback(() => {
        const container = createPoContainerRef.current;
        if (container) {
            container.scrollTo({ top: 244, behavior: 'smooth' });
        }
    }, []);

    const initializeBuyerPaymentPreferences = async (buyingPreference: any) => {
        try {
            const res = await getAvailableCreditLimit.mutateAsync();
            if (res?.data?.data !== 'SUCCESS') {
                if (
                    typeof res.data.data === "object" &&
                    "error_message" in res.data.data
                ) {
                    setValue('availableCreditLimit', 0)
                    setValue('creditLimitStatus', creditLimitStatus.REJECTED);
                    buyingPreference.bnpl_settings = undefined;
                } else {
                    const creditLimitData = res.data.data;
                    setValue('availableCreditLimit', creditLimitData?.available_credit_limit)
                    setValue('creditLimitStatus', creditLimitData?.status);
                }
            }
        } catch (error) {
            console.error('Error fetching buyer preferences:', error);
        }
    }

    // useEffect(() => {
    //     if (deboucedSearchString && isCreatePOModule) {
    //         handleCreatePOSearch(searchStringData, null, lineSessionId)
    //     }
    // }, [deboucedSearchString])

    const updateLineProductTag = useCallback((index, product) => {
        if (userPartData && Object.keys(userPartData)?.length) {
            setValue(`cart_items.${index}.product_tag`, userPartData[product?.Product_ID])
        } else {
            setValue(`cart_items.${index}.product_tag`, "")
        }
    }, [userPartData, setValue]);

    const resetQtyAndPricePerUnitFields = (index, product) => {
        const qtyUnitMData = product.QUM_Dropdown_Options?.split(",")
        setValue(`cart_items.${index}.qty_um`, qtyUnitMData);
        if (!initialData?.cart_items?.[index]?.qty_unit) {
            setValue(`cart_items.${index}.qty_unit`, qtyUnitMData[0])
        }
        resetPricePerUnitFields(index, product);
    }

    const resetPricePerUnitFields = (index, product) => {
        const priceUnitMData = product?.PUM_Dropdown_Options?.split(",").filter((item: any) => !(item.trim().toLowerCase() === priceUnits.net_ton || item.trim().toLowerCase() === 'net ton'));

        setValue(`cart_items.${index}.price_um`, priceUnitMData);
        if (!initialData?.cart_items?.[index]?.price_unit) {
            let priceUnit = priceUnitMData[0];
            if (initialData?.cart_items?.[index]?.bom_line_id && initialData?.cart_items?.[index]?.qty_unit) priceUnit = initialData?.cart_items?.[index]?.qty_unit;
            setValue(`cart_items.${index}.price_unit`, priceUnit)
        } else {
            setValue(`cart_items.${index}.price_unit`, initialData?.cart_items?.[index]?.price_unit)
        }
        pricePerUnitChangeHandler(index, product);
    }

    const validateSavedBomCreatePo = useCallback((index) => {
        const item = watch(`cart_items.${index}`);
        let descriptionObj = watch(`cart_items.${index}`)?.descriptionObj;
        if (item?.selected_products?.length && !descriptionObj) {
            const selectedProducts = item.selected_products;
            const hasSelectedProducts = selectedProducts.length > 0;
            if (hasSelectedProducts) {
                const product = productMapping[selectedProducts[0]];
                // Directly set the values on the cartItem object
                descriptionObj = product;
            }
        }
        if (descriptionObj && Object.keys(descriptionObj).length > 0) {
            const _selected = descriptionObj;

            if (_selected) {
                const qtyVal = +getValues(`cart_items.${index}.qty`) || 0;
                const qtyUnit = getValues(`cart_items.${index}.qty_unit`);
                const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options?.split(",")[0] : '')).toLowerCase();
                const updatedUnit = unit;
                const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);
                if (qtyVal && orderIncrement) {
                    if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                        clearErrors(`cart_items.${index}.qty`);
                        trigger(`cart_items.${index}.qty`);
                        return true;
                    } else {
                        if (_selected) setError(`cart_items.${index}.qty`, { message: `Quantity can only be multiples of ${orderIncrement}` }, { shouldFocus: false })
                        if (qtyVal === null) setValue(`cart_items.${index}.qty`, '');
                        setValue(`cart_items.${index}.extended`, 0);
                        setValue(`cart_items.${index}.seller_extended`, 0);
                    }
                }
            }
        }
    }, [watch, getValues, productMapping, setError, setValue, clearErrors, trigger]);

    const pricePerUnitChangeHandler = useCallback((index: any, product: any, priceData: any = 0) => {
        product = product ?? getValues(`cart_items.${index}.descriptionObj`);
        if (product) {
            const unit = getValues(`cart_items.${index}.price_unit`) || product.PUM_Dropdown_Options.split(",")[0];
            const umVal = getValUsingUnitKey(product, unit, newPricingPrefix);
            setValue(`cart_items.${index}.price`, umVal);
            setValue(`cart_items.${index}.seller_price`, umVal);
        }
        else {
            setValue(`cart_items.${index}.price`, 0);
            setValue(`cart_items.${index}.seller_price`, 0);
        }
    }, [getValues, setValue, newPricingPrefix]);

    const saveUserActivity = (checkOutPoNumber, checkOutError) => {
        // if (isCreatePOModule) {
        //     saveCreatePOUserActivity(sessionId, checkOutPoNumber, checkOutError);
        // }
    }

    const saveBomHeaderDetails = () => {
        if (location.pathname === routes.bomUploadReview || location.pathname === routes.savedBom) {
            if (currentBomData?.id) {
                setBomDataIdToRefresh(currentBomData?.id);
            }
            try {
                const headerFormData = getHeaderFormData();
                const formattedHeaderFormData = {
                    "bom_name": headerFormData.internal_po_number,
                    "bom_type": headerFormData.order_type,
                    "delivery_date": headerFormData.delivery_date,
                    ...headerFormData
                }
                
                const payload = {
                    "data": {
                        "bom_upload_id": currentBomData?.id ?? '',
                        ...formattedHeaderFormData
                    }
                }
                if (currentBomData?.id && payload.data.bom_name && payload.data.bom_type && payload.data.delivery_date && payload.data.shipping_details.line1 && payload.data.shipping_details.city && payload.data.shipping_details.zip && payload.data.shipping_details.state_id) {
                    saveModifiedBomHeader();
                    mutateSaveBomHeaderDetails.mutateAsync(payload)
                    let _updatedBomInitialData = {
                        ...uploadBomInitialData,
                        ...headerFormData
                    }
                    setUploadBomInitialData(_updatedBomInitialData)
                }
            } catch (error) {
                console.error(error)
            }
        }
    }

    const saveModifiedBomHeader = () => {
        try {
            if (currentBomData?.id) {
                const data = {
                    [currentBomData?.id]: "HEADER"
                }
                const _lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
                if (_lastModifiedBom) {
                    const _lastModifiedBomData = JSON.parse(_lastModifiedBom);
                    _lastModifiedBomData[currentBomData?.id] = "HEADER";
                    localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(_lastModifiedBomData));
                } else {
                    localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(data));
                }
            }
        } catch (e) {
            console.warn('Could not store value in localStorage', e);
        }
    }

    // Validates that cart items have required fields if they have a description
    const validateCart = () => {
        const cartItems = getValues('cart_items');
        if (!cartItems?.length) return true;

        // Only validate items that have a description
        const itemsWithDescription = cartItems.filter(item => item?.descriptionObj?.UI_Description && item?.lineStatus !== "SKIPPED" || item?.lineStatus === "APPROVED");
        if (!itemsWithDescription.length) return false;

        // Check required fields are present and valid
        return itemsWithDescription.every(item => {
            const quantity = Number(item.qty);
            return quantity > 0 &&
                Boolean(item.qty_unit) &&
                Boolean(item.price_unit);
        });
    };

    // Monitor cart changes and update validation state
    // useEffect(() => {
    //     const subscription = watch((_, { name }) => {
    //         if (name?.startsWith('cart_items')) {
    //             setIsCartValid(validateCart());
    //         }
    //     });
    //     return () => subscription.unsubscribe();
    // }, [watch]);

    

    const scrollToTop = useCallback(() => {
        if (scrollerRef.current) {
            scrollerRef.current.updateScrollPosition(0);
        }
        const container = createPoContainerRef.current;
        if (container) {
            container.scrollTo({ top: 0, behavior: 'smooth' });
        }
    }, []);

    const handleMainScroll = () => {
        // Skip if the scroll is from dragging the thumb
        // if (isDraggingScrollState) return;

        if (!createPoContainerRef.current) return;

        const { scrollTop, scrollHeight, clientHeight } = createPoContainerRef.current;
        const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 5; // 5px threshold
        // if (!isDraggingScrollState) setScrollPosition(scrollTop);
        console.log("scrollTop",scrollHeight - scrollTop - clientHeight, scrollHeight, scrollTop, clientHeight);
        if(scrollerRef.current) scrollerRef.current.updateScrollPosition(scrollTop);
        const header = headerContainerRef.current;
        console.log("isAtBottom", isAtBottom);
        // Handle header opacity
        if (header) {
            let opacity = 0;
            if (scrollTop > 52) {
                opacity = Math.min(scrollTop / 152, 1);
            }
            (header as HTMLElement).style.opacity = opacity.toString();
        }
        // Enable/disable the table scroll based on main scroll position
        if (addPoLineTableRef.current) {
            setHidePoLineScroll(!isAtBottom);
        }
    }

    // useEffect(() => {
    //     if(!hidePoLineScroll){
    //         setTimeout(() => {
    //             if(scrollerRef.current) scrollerRef.current.updateScrollPosition((addPoLineTableRef.current?.scrollTop??0) + 204);
    //         }, 400);
    //     }
    // }, [hidePoLineScroll])

    const handleScrollerChange = (newScrollPosition: number) => {
        let headerScroll = 0;
        let tableScroll = 0;
        console.log("newScrollPosition", newScrollPosition);
        if(newScrollPosition >= 0 && newScrollPosition < 204){
            headerScroll = newScrollPosition;
            tableScroll = 0;
        }else {
            headerScroll = 204;
            tableScroll = newScrollPosition - 204;
        }
        if(createPoContainerRef.current)
            createPoContainerRef.current.scrollTop = headerScroll;
        if(addPoLineTableRef.current)
            addPoLineTableRef.current.scrollTop = tableScroll;
    }

    console.log("-------BOM REVIEW COMPONENT RENDERED----------");

    return (
        <div>
            <div className={clsx(styles.createPoContent, 'bgBlurContent')}>
                <div>
                    <SearchHeader />
                </div>
                <div className={styles.formInnerContent} ref={HeaderDetailsConfirmedRef}>
                    {/* <div className={styles.headerNoteCreatePO}>
                                <span className={styles.leftIcon}><WarningIcon /></span>

                                <span className={styles.headerNoteText}>All delivered material will be new (aka "prime"), fulfilled to the defined specification,
                                    loaded/packaged for forklift and/or magnetic offloading and have mill test reports.</span>
                                <span className={clsx(styles.headerNoteText, styles.marginTop8)}>The maximum bundle weight is 5,000 pounds.</span>
                                <span className={styles.rightIcon}><WarningIcon /></span>
                            </div> */}
                    <div className={clsx(styles.tblWrapper, 'w100')}>
                            <Scroller
                                ref={scrollerRef}
                                containerHeight={880}
                                contentHeight={maxScrollHeight}
                                onScrollChange={handleScrollerChange}
                                rightOffset={5}
                                bottomOffset={20}
                                topOffset={20}
                                />
                                <>
                                    <div className={clsx(styles.headerContainer)} ref={headerContainerRef} onClick={scrollToTop} >
                                        <div className={styles.headerItem}>
                                            {poHeaderFormWatch?.('internal_po_number')?.toUpperCase() || '-'}
                                        </div>
                                        <div className={styles.headerItem}>
                                            {poHeaderFormWatch?.('delivery_date') ?
                                                `${dayjs(poHeaderFormWatch?.('delivery_date')).format('ddd').toUpperCase()}, ${poHeaderFormWatch?.('delivery_date')}`
                                                : '-'
                                            }
                                        </div>
                                        <div className={styles.headerItem}>
                                            {poHeaderFormWatch?.('order_type')?.toUpperCase() || '-'}
                                        </div>
                                        <div className={styles.headerItem}>
                                            {<><span>{(poHeaderFormWatch?.('shipping_details.line1')?.toUpperCase() || '-')}</span> <span>{(poHeaderFormWatch?.('shipping_details.line2')?.toUpperCase() || '')}</span></>}
                                        </div>
                                    </div>
                                    <div className={clsx(styles.createPOContainer, styles.removeFooter)} ref={createPoContainerRef} onScroll={handleMainScroll}>
                                        <CreatePoHeaderInfo
                                            styles={styles}
                                            ref={createPoHeaderInfoRef}
                                            formInputGroupRef={formInputGroupRef}
                                            orderInfoIsFilled={orderInfoIsFilled}
                                            isCalendarOpen={isCalendarOpen}
                                            setIsCalendarOpen={setIsCalendarOpen}
                                            setOpenErrorDialog={setOpenErrorDialog}
                                            setErrorMessage={setErrorMessage}
                                            saveUserActivity={saveUserActivity}
                                            saveBomHeaderDetails={saveBomHeaderDetails}
                                            disableBidBuyNow={disableBidBuyNow}
                                            setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                            openDeliveryToDialog={openDeliveryToDialog}
                                            scrollToTop={scrollToTop}
                                            isSavedBom={isSavedBom}
                                            focusJobPoInput={focusJobPoInput}
                                        />
                                        <BomReviewTable 
                                            styles={styles}
                                            createPoContainerRef={createPoContainerRef}
                                            formInputGroupRef={formInputGroupRef}
                                            orderInfoIsFilled={orderInfoIsFilled}
                                            hidePoLineScroll={hidePoLineScroll}
                                            setHidePoLineScroll={setHidePoLineScroll}
                                            filteredFields={filteredFields}
                                            addPoLineTableRef={addPoLineTableRef}
                                            bomUploadResult={bomUploadResult}
                                            register={register}
                                            fields={fields}
                                            products={products}
                                            setValue={setValue}
                                            watch={watch}
                                            errors={errors}
                                            control={control}
                                            getValues={getValues}
                                            pricePerUnitChangeHandler={pricePerUnitChangeHandler}
                                            userPartData={userPartData}
                                            sessionId={sessionId}
                                            searchStringData={searchStringData}
                                            setSearchString={setSearchString}
                                            setLineSessionId={setLineSessionId}
                                            lineSessionId={lineSessionId}
                                            setDisableBidBuyNow={setDisableBidBuyNow}
                                            setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                            scrollToTop={scrollToTop}
                                            clearErrors={clearErrors}
                                            currentBomData={currentBomData}
                                            updateLineProductTag={updateLineProductTag}
                                            validateSavedBomCreatePo={validateSavedBomCreatePo}
                                            scrollPoHeaderToBottom={scrollPoHeaderToBottom}
                                            setFocusJobPoInput={setFocusJobPoInput}
                                            scrollerRef={scrollerRef}
                                        />
                                    </div>
                                </>
                    </div>
                    <Dialog
                        open={openErrorDialog}
                        onClose={(event) => setOpenErrorDialog(false)}
                        transitionDuration={200}
                        hideBackdrop
                        disableScrollLock={true}
                        container={HeaderDetailsConfirmedRef.current}
                        classes={{
                            root: styles.ErrorDialog,
                            paper: styles.dialogContent
                        }}

                    >
                        <p>{errorMessage}</p>
                        <button className={styles.submitBtn} onClick={(event) => { setOpenErrorDialog(false); }}>Ok</button>
                    </Dialog>

                    <Dialog
                        open={!isHeaderDetailsConfirmed && cameFromSavedBom}
                        transitionDuration={200}
                        disableScrollLock={true}
                        container={HeaderDetailsConfirmedRef.current}
                        style={{
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'rgba(255, 255, 255, 0.1)',
                            border: '1px solid transparent',
                            borderRadius: '0px 0px 20px 20px',
                        }}
                        PaperProps={{
                            style: {
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                                margin: 0
                            }
                        }}
                        hideBackdrop
                        classes={{
                            root: styles.confirmHeaderDetailsPopup,
                            paper: styles.dialogContent
                        }}

                    >

                        <div className={styles.confirmHeaderDetailsContainer}>
                            <span>CONFIRM HEADER DETAILS</span>
                            <button onClick={() => {
                                setIsHeaderDetailsConfirmed(true);
                            }}>PROCEED</button>
                        </div>


                    </Dialog>

                </div>
            </div>
        </div>
    )
}


const BomReview = () => {
    const location = useLocation(); // Add this line to get location

    return (
        <BomReviewComponent  key={location.pathname}/>
    )
}

export default BomReview