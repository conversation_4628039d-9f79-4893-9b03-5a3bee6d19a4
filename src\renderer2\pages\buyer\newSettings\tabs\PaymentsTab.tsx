import React, { useCallback, useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import clsx from 'clsx';
import { CardCvcElement, CardExpiryElement, CardNumberElement, useElements, useStripe } from '@stripe/react-stripe-js';
import { buyerSettingConst, ueGetBuyingPreference, useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { dispatchRaygunError, formatCurrency, formatCurrencyWithComma, formatEIN, formatToTwoDecimalPlaces, removeCommaFromCurrency, trueVaultRaygunError } from 'src/renderer2/helper';
import { useFavicon } from '@mantine/hooks';
import { commomKeys, userRole } from 'src/renderer2/common';
import axios from 'axios';
import TrueVaultClient from 'truevault';
import { ClickAwayListener, Dialog } from '@mui/material';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import usePostBuyerSettingsPayment from 'src/renderer2/hooks/usePostBuyerSettingsPayment';
import usePostCancelBnplRequest from 'src/renderer2/hooks/usePostCancelBnplRequest';

interface InputFocusState {
  creditLimit: boolean;
  outstanding: boolean;
  available: boolean;
  bankName: boolean;
  accountName: boolean;
  bankRoutingNumber: boolean;
  bankAccountNumber: boolean;
  billingZipCode: boolean;
  dnBNumber: boolean;
  einNumber: boolean;
  creditLine: boolean;
  requestCreditLine: boolean;
}

// Add custom styling for Stripe Elements
const stripeElementsStyle = {
  base: {
    color: '#fff',
    fontFamily: 'Inter, sans-serif',
    fontSize: '18px',
    '::placeholder': {
      color: "rgba(255, 255, 255, 0.2)",
    },
    padding: '6px 16px',
  },
  invalid: {
    color: '#ff6b6b',
    iconColor: '#ff6b6b',
  },
};


const PaymentsTab: React.FC = ({ control, errors, setError, setValue, watch, isDirty, isValid, register, handleSubmit, getValues, trigger, isBnplFilled, clearErrors, isBnplApproved, bnplCreditStatus, setIsBnplApproved, setBnplCreditStatus, bnplStatus , setBnplStatus, creditStatus, setCreditStatus }: any) => {
  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    creditLimit: false,
    outstanding: false,
    available: false,
    bankName: false,
    accountName: false,
    bankRoutingNumber: false,
    bankAccountNumber: false,
    billingZipCode: false,
    dnBNumber: false,
    einNumber: false,
    creditLine: false,
    requestCreditLine: false
  });
  const [stripeError, setStripeError] = useState<{
    cardNumber: string | null,
    cardExpiry: string | null,
    cardCvv: string | null,
  }>({
    cardNumber: null,
    cardExpiry: null,
    cardCvv: null,
  });
  const [cardBrand, setCardBrand] = useState<string | null>(null);

  const stripe = useStripe();
  const elements = useElements();
  const { userData, subscriptionStatus, setShowLoader, setSubscriptionStatus, referenceData }: any = useGlobalStore();
  const { setBuyerSettingInfo } = useBuyerSettingStore();
  const [net30Default, setNet30Default] = useState<string | null>(null);
  const [achDefault, setAchDefault] = useState<string | null>(null);
  const [cardComplete, setCardComplete] = useState({
    cardNumber: false,
    cardExpiry: false,
    cardCvv: false
  });
  const userSubscription = {
    card_number_last_four_digits: null,
    cardExpiry: null,
  }
  const [requestCreditLineDialogOpen, setRequestCreditLineDialogOpen] = useState(false);
  const {showCommonDialog, resetDialogStore}: any = useDialogStore();
  const maskedRoutingNo = getValues('refRoutingNo') ? '*'.repeat(getValues('refRoutingNo').length) : '';
  const maskedAccountNo = getValues('refAccountNo') ? '*'.repeat(getValues('refAccountNo').length) : '';
  const [showMaskedAchDetails, setShowMaskedAchDetails] = useState(true);
  const paymentCreditReq = useRef(null);
  const [editModeDesiredCreditLine, setEditModeDesiredCreditLine] = useState(false);
  const [validationInProgress, setValidationInProgress] = useState({
    submitBnpl: false,
    requestCreditLine: false,
    cancelIncreaseCreditLimitRequest: false,
    cancelBnplRequest: false
  });
  const {
    mutateAsync: buyerSettingsPayment
  } = usePostBuyerSettingsPayment();

  const {
    mutateAsync: cancelBnplRequest
  } = usePostCancelBnplRequest();
  
  const getBuyingPreference = ueGetBuyingPreference();

  useEffect(() => {
    return () => {
      setValue('requestCreditLine', '')
    }
  }, []);


  useEffect(() => {
    if (userData?.data?.id && referenceData) {
      referenceData?.ref_pgpm_mapping.map((paymentMethod: any) => {
        if (paymentMethod.user_type === userRole.buyerUser.toLowerCase() && paymentMethod.payment_gateway === 'BALANCE') {
            setNet30Default(paymentMethod.payment_method);
        }
        if (paymentMethod.user_type === userRole.buyerUser.toLowerCase() && paymentMethod.payment_gateway === 'DEFAULT') {
            setAchDefault(paymentMethod.payment_method);
        }
    })
    if (referenceData.ref_account_routing_number[0]) {
      if (referenceData.ref_account_routing_number[0]?.account_number) {
          setValue('refAccountNo', referenceData.ref_account_routing_number[0]?.account_number);
      }
      if (referenceData.ref_account_routing_number[0]?.routing_number) {
          setValue('refRoutingNo', referenceData.ref_account_routing_number[0]?.routing_number);
      }
      if (referenceData.ref_account_routing_number[0]?.account_name) {
        setValue('accountName', referenceData.ref_account_routing_number[0]?.account_name);
      }
  }
    }
  }, [referenceData, userData]);

  const handleCardChange = useCallback((event: any, fieldName: keyof typeof cardComplete) => {
    // setIsCardDetailsRequired(true);
    setCardComplete(prev => ({
      ...prev,
      [fieldName]: event.complete
    }));

    // Capture card brand when it's a card number element
    if (fieldName === 'cardNumber' && event.brand && event.brand !== 'unknown') {
      setCardBrand(event.brand);
      setValue('cardType', event.brand.toUpperCase());
    }

    // Set or clear error based on the event
    if (event.error || (!event.empty && !event.complete)) {
      setStripeError(prev => ({
        ...prev,
        [fieldName]: event.error?.message ?? 'Incomplete card details'
      }));
    } else {
      setStripeError(prev => ({
        ...prev,
        [fieldName]: null
      }));
    }
  }, [stripeError, setValue]);

  const handleCardSubmit = async () => {
    try {
      const isValid = await trigger(['billingZipCode']);
      if (!stripeError.cardNumber && !stripeError.cardExpiry && !stripeError.cardCvv && isValid) {
        if (!stripe || !elements) {
          // Stripe.js hasn't loaded yet
          setStripeError("Stripe hasn't loaded yet. Please try again.");
          setShowLoader(false);
          return;
      }
        let _paymentMethod;
        const cardElement = elements?.getElement(CardNumberElement);
        if (cardComplete.cardNumber && cardComplete.cardExpiry && cardComplete.cardCvv && watch('billingZipCode')) {
          const { paymentMethod, error: paymentMethodError }: any = await stripe?.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
              email: watch('cardEmailId'),
              name: `${watch('cardFirstName')} ${watch('cardLastName')}`,
              address: {
                country: 'US',
                postal_code: watch('billingZipCode'),
              }
            }
          });
          if (paymentMethodError) {
            setStripeError(paymentMethodError.message || 'An error occurred during payment processing');
            setShowLoader(false);
            return;
          }
          else {
            _paymentMethod = paymentMethod;
          }
        }
        
        const payload = {
          "data": {
            "payment_method": "credit_card",
            "payment_details": {
              "zipcode": watch('billingZipCode'),
              "payment_method_id": _paymentMethod?.id ? _paymentMethod.id : undefined
            }
          }
        }
        await buyerSettingsPayment(payload);
      }
      
      let response;
      // if (userSubscription.active_customer || isUpdatePaymentModule) {
      //   response = await updateUserPayment(payload);
      //   setSubscriptionStatus(true);
      // } else {
      //   response = await saveUserSubscription(payload);
      //   if (!response || !response?.client_secret) {
      //     setStripeError('No client secret received from server');
      //     setShowLoader(false);
      //     return;
      //   }

      //   const { client_secret: clientSecret } = response;
      //   const { paymentIntent, error: confirmError } = await stripe.confirmCardPayment(clientSecret);
      //   if (confirmError) {
      //     setStripeError(confirmError.message || 'An error occurred during payment processing');
      //   } else if (paymentIntent.status === 'succeeded') {

      //   }
      // }
    } catch (error: unknown) {
      const err = error as Error;
      setStripeError(prev => ({
        ...prev,
        cardNumber: err.message || 'An error occurred during payment processing'
      }));
      showCommonDialog(
        null,
        err.message || 'An error occurred during payment processing',
        commomKeys.actionStatus.error,
        resetDialogStore,
        [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
      );
      setShowLoader(false);
    }
  }

  const handleEinNoChange = (e: any) => {
    const { value } = e.target;
    setValue('einNumber', formatEIN(value));
  }

  const requestCreditLineChangeHandler = (e: any, fieldName: string) => {
    let value = sanitizeNumberInput(e.target.value);
    clearErrors(fieldName);
    
    if (!isNaN(removeCommaFromCurrency(value))) {
      setValue(fieldName, removeCommaFromCurrency(value));
    }
  }

  // Function to sanitize input - removes negative signs, spaces, and keeps only numbers
  const sanitizeNumberInput = (value: string): string => {
    // Remove negative signs, spaces, and keep only digits and decimal points
    return value.replace(/[-\s]/g, '').replace(/[^0-9.]/g, '');
  }

  const isBnplSubmitDisabled = watch('dnBNumber') && watch('creditLine') && watch('einNumber') && !validationInProgress.submitBnpl;

  const bnplSetupValidate = async () => {
    if (!watch('dnBNumber') || !watch('creditLine') || !watch('einNumber')) {
      const net30Fields = ['creditLine', 'dnBNumber', 'einNumber'];
      for (let trial in net30Fields) {
        if (watch(net30Fields[trial]) === '') {
          setError(net30Fields[trial], { message: `Net 30 Terms is not valid` });
        }
      }

    } else {
      const isValid = await trigger(['dnBNumber', 'creditLine', 'einNumber']);
      if (!isValid) {
        return;
      }
      if (+watch('creditLine') <= 0) {
        setError('creditLine', { message: `Net 30 Terms is not valid` });
        return;
      }
      if (watch('creditLine') > buyerSettingConst.buyerCreditLineLimit) {
        setError('creditLine', { message: buyerSettingConst.creditLimitErrorMessage }, { shouldFocus: true });
        return
      }
      clearErrors(['dnBNumber', 'creditLine', 'einNumber']);
      applyNetData();
    }
  }

  const applyNetData = () => {
    setValidationInProgress({
      ...validationInProgress,
      submitBnpl: true
    });
    getTruevaultData(getValues('parentCompanyName'), userData.data.id, null, null, null, net30Default, getValues('einNumber'), getValues('dnBNumber')).then(documentIdFromTruevault => {
      const payload = {
        "data": {
          "ein_number": getValues('einNumber').slice(-2).padStart(getValues('einNumber').length, 'x'),
          "duns_number": getValues('dnBNumber'),
          "agreed_terms": true,
          "desired_credit_limit": getValues('creditLine'),
          "pgpm_mapping_id": 4,
          "reference_document_id": documentIdFromTruevault ? documentIdFromTruevault : ''
        }
      };

      axios.post(import.meta.env.VITE_API_SERVICE + '/user/saveBuyNowPayLaterData', payload, {
        headers: {
          UserId: userData.data.id
        }
      })
        .then(res => {
          if (res.status === 200) {
            setBnplStatus('PENDING');
            setValidationInProgress({
              ...validationInProgress,
              submitBnpl: false
            });

            getBuyingPreference.mutateAsync().then(res => {
              if (res?.data?.data !== 'SUCCESS') {
                const buyerPreferenceData = res.data.data;
                setBuyerSettingInfo(buyerPreferenceData);
              }
            });
            // setSuccessAppSubmit(true)
            // setNet30ApplyStatus(true);
          } else if(res?.data?.data.error_message) {
            showCommonDialog(
              null,
              commomKeys.errorContent,
              commomKeys.actionStatus.error,
              resetDialogStore,
              [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
            );
            }
        })
        .catch(err => {
          console.error(err);
          setValidationInProgress({
            ...validationInProgress,
            submitBnpl: false
          });
          showCommonDialog(
            null,
            commomKeys.errorContent,
            commomKeys.actionStatus.error,
            resetDialogStore,
            [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
          );
          // setApiFailureDialog(true);
        });
    })

  }

  const getTruevaultData = async (companyName, userData, bankName, routingNo, accountNo, paymentMethod, einNumber, dnBNumber) => {
    try {
      const res = await axios.get(import.meta.env.VITE_API_SERVICE + '/user/getAccessToken');
      const accessToken = res.data.data;
      let buyerPaymentData = {};
      if (paymentMethod === achDefault) {
        buyerPaymentData = {
          "document": {
            "company_name": companyName,
            "user_id": userData,
            "bank_name": bankName,
            "routing_number": routingNo,
            "account_number": accountNo,
            "payment_method": paymentMethod
          }
        }
      }
      else if (paymentMethod === net30Default) {
        buyerPaymentData = {
          "document": {
            "company_name": companyName,
            "user_id": userData,
            "dnb_number": dnBNumber,
            "net_30_ein": einNumber,
            "payment_method": paymentMethod
          }
        }
      }

      const client = new TrueVaultClient({ accessToken });

      try {
        const response = await client.createDocument(import.meta.env.VITE_TRUE_VAULT_ID_BUYER_VAULT_ID, null, buyerPaymentData);
        const documentIdFromTruevault = response.id;
        if (!documentIdFromTruevault) {
          dispatchRaygunError(new Error('TrueVault error: TruevoltObject = ' + JSON.stringify(response)), [trueVaultRaygunError]);
        }
        return documentIdFromTruevault;

      } catch (error) {
        console.error("Error creating document:", error);
        dispatchRaygunError(error, [trueVaultRaygunError]);
        setValidationInProgress({
          ...validationInProgress,
          submitBnpl: false
        });
      }

    } catch (err) {
      console.error(err)
    }

  }

  const submitRequestCreditLine = () => {
    let unFormattedCreditLineValue = removeCommaFromCurrency(getValues('requestCreditLine'))
    if (Number(unFormattedCreditLineValue) > buyerSettingConst.buyerCreditLineLimit) {
        setError('requestCreditLine', { message: buyerSettingConst.creditLimitErrorMessage }, { shouldFocus: true });
        // showErrorKeyVal(['requestCreditLine'])
        return;
    }
    clearErrors();
    if (Number(unFormattedCreditLineValue) > 0) {
      setValidationInProgress({
        ...validationInProgress,
        requestCreditLine: true
      });
        const payload = {
            "data": {
                "request_increase_credit": unFormattedCreditLineValue
            }
        }
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/increaseCreditLimitRequest', payload)
            .then(res => {
                if (res?.data?.data?.error_message) {
                    showCommonDialog(null, res.data.data.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
                } else {
                    setBnplCreditStatus('Pending Increase')
                    setValue('requestedIncreaseCredit', formatCurrency(parseFloat(unFormattedCreditLineValue)))
                    setRequestCreditLineDialogOpen(false);
                }
                setValue('requestCreditLine', '')
                setValidationInProgress({
                  ...validationInProgress,
                  requestCreditLine: false
                });
                // setRequestCreditLineAmount(false)
            })
            .catch(err => {
                console.error(err);
                setShowLoader(false);
                setValidationInProgress({
                  ...validationInProgress,
                  requestCreditLine: false
                });
                // setApiFailureDialog(true)
            }
            );
    } else {
        setError('requestCreditLine', 'field is not vaild', { shouldFocus: true });
        setError('type', 'error checking')
        setValidationInProgress({
          ...validationInProgress,
          requestCreditLine: false
        });
    }
  }

  const handleCancelRequestCreditLine = () => {
    setValidationInProgress({
      ...validationInProgress,
      cancelIncreaseCreditLimitRequest: true
    });
      axios.post(import.meta.env.VITE_API_SERVICE + `/user/cancelIncreaseCreditLimitRequest`)
          .then(res => {
              setBnplCreditStatus(null);
              if(res?.data?.data?.error_message){
                showCommonDialog(
                  null,
                  res?.data?.data?.error_message || commomKeys.errorContent,
                  commomKeys.actionStatus.error,
                  resetDialogStore,
                  [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
                );
              }
              setValidationInProgress({
                ...validationInProgress,
                cancelIncreaseCreditLimitRequest: false
              });
          })
          .catch(err => {
              console.error(err);
              setShowLoader(false);
              setValidationInProgress({
                ...validationInProgress,
                cancelIncreaseCreditLimitRequest: false
              });
              showCommonDialog(
                null,
                commomKeys.errorContent,
                commomKeys.actionStatus.error,
                resetDialogStore,
                [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
              );
              // setApiFailureDialog(true)
          });
  }

  const handleRevealCashAdvance = () => {
    setShowMaskedAchDetails(false);
  }

  const handleCancelBnplRequest = () => {
    try {
      setValidationInProgress({
        ...validationInProgress,
        cancelBnplRequest: true
      });
      cancelBnplRequest().then(() => {
        setBnplStatus('');
        setValue('dnBNumber', '');
        setValue('einNumber', '');
        setValue('creditLine', '');
        setValidationInProgress({
          ...validationInProgress,
          cancelBnplRequest: false
        });
      });
    } catch (error) {
      console.error(error);
      showCommonDialog(
        null,
        error?.message || commomKeys.errorContent,
        commomKeys.actionStatus.error,
        resetDialogStore,
        [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
      );
      setValidationInProgress({
        ...validationInProgress,
        cancelBnplRequest: false
      });
    }
  }

  return (
    <div className={clsx(styles.tabContent, styles.paymentTabContent)} ref={paymentCreditReq}>
      <div className={styles.scrollerContainer}>
      
      <div className={styles.formContainer}>
        <div className={styles.formGroupInputContainer}>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={styles.paymentHeader} htmlFor="bankName">
                NET 30 TERMS
              </label>
            </span>
            <span className={styles.col1}>
              {
                requestCreditLineDialogOpen ? <>
                  <div className={clsx(styles.formGroupInput,styles.net30TermsRequestCreditLine)}>
                    <span className={styles.col1}>
                      <InputWrapper>
                        <CustomTextField
                          className={clsx(styles.inputCreateAccount, errors?.requestCreditLine && styles.error)}
                          type='text'
                          mode="commaNumber"
                          register={register("requestCreditLine")}
                          placeholder='Enter Amount ($)'
                        />
                      </InputWrapper>
                    </span>
                    <button className={styles.headerBtn} onClick={() => submitRequestCreditLine()} disabled={validationInProgress.requestCreditLine}><span>SUBMIT</span></button>
                  </div>
                </> :
                (

                    bnplStatus && bnplStatus !== 'REJECTED' ? (
                      bnplStatus === 'PENDING' ?
                          <div className={styles.bnplStatusContainer}>
                            <button className={styles.headerBtn} disabled={validationInProgress.cancelBnplRequest} onClick={handleCancelBnplRequest}><span>CANCEL</span></button>
                            <span className={styles.pendingReviewStatus}>PENDING REVIEW</span>
                          </div>
                        : bnplCreditStatus && bnplCreditStatus === 'Pending Increase' ?
                          <div className={styles.bnplStatusContainer}>
                            <button className={styles.headerBtn} onClick={handleCancelRequestCreditLine} disabled={validationInProgress.cancelIncreaseCreditLimitRequest}><span>CANCEL</span></button>
                            <span className={styles.pendingStatus}>CL INCREASE PENDING REVIEW</span>
                          </div>
                          :
                          <div className={styles.bnplStatusContainer}>
                            <button className={styles.headerBtn}  onClick={() => setRequestCreditLineDialogOpen(true)}><span>REQUEST CL INCREASE</span></button>
                            <span className={styles.approvedStatus}>APPROVED</span>
                          </div>
                    )
                      :
                      <>
                        {
                          watch('creditLine') || watch('dnBNumber') || watch('einNumber') ?
                            <button className={styles.headerBtn} disabled={!isBnplSubmitDisabled} onClick={() => bnplSetupValidate()}><span>SUBMIT</span></button>
                            :
                            <button className={styles.headerBtn} onClick={() => trigger(['creditLine', 'dnBNumber', 'einNumber'])}><span>APPLY FOR TERMS</span></button>
                        }
                      </>
                )
              }
     
            </span>
          </div>
          {
            bnplStatus && bnplStatus !== 'PENDING' && bnplStatus !== 'REJECTED' ? (
              <>
                <div className={styles.formGroupInput}>
                  <span className={styles.col1}>
                    <label htmlFor="balanceCreditLimit">
                      CREDIT LIMIT
                    </label>
                  </span>
                  <span className={styles.col1}>
                      <span className={styles.inputCreateAccount}>
                        {watch('balanceCreditLimit')}
                      </span>
                  </span>
                </div>
                <div className={styles.formGroupInput}>
                  <span className={styles.col1}>
                    <label htmlFor="outstandingAmount">
                      OUTSTANDING
                    </label>
                  </span>
                  <span className={styles.col1}>
                    <span className={styles.inputCreateAccount}>
                      {watch('outstandingAmount')}
                    </span>
                  </span>
                </div>
                <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
                  <span className={styles.col1}>
                    <label htmlFor="availableBalance">
                      AVAILABLE
                    </label>
                  </span>
                  <span className={styles.col1}>
                    <span className={styles.inputCreateAccount}>
                      {watch('availableBalance')}
                    </span>
                  </span>
                </div>
              </>
            ) :
              bnplStatus === 'PENDING' ? 
              (
                <>
                  <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                      <label className={clsx(isInputFocused.dnBNumber && styles.focusLbl)} htmlFor="dnBNumber">
                        D&B NUMBER
                      </label>
                    </span>
                    <span className={styles.col1}>
                      <span className={styles.inputCreateAccount}>
                        {watch('dnBNumber')}
                      </span>
                    </span>
                  </div>
                  <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                      <label className={clsx(isInputFocused.einNumber && styles.focusLbl)} htmlFor="einNumber">
                        COMPANY EIN
                      </label>
                    </span>
                    <span className={styles.col1}>
                      <span className={styles.inputCreateAccount}>
                        {watch('einNumber')}
                      </span>
                    </span>
                  </div>
                  <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
                    <span className={styles.col1}>
                      <label className={clsx(isInputFocused.creditLine && styles.focusLbl)} htmlFor="creditLine">
                        DESIRED CREDIT LIMIT
                      </label>
                    </span>
                    <span className={styles.col1}>
                      <span className={styles.inputCreateAccount}>
                      $ { watch("creditLine") ? formatToTwoDecimalPlaces(watch("creditLine") ) : ''}
                      </span>
                    </span>
                  </div>``
                </>
              ) : 
              (
                <>
                  <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                      <label className={clsx(isInputFocused.dnBNumber && styles.focusLbl)} htmlFor="dnBNumber">
                        D&B NUMBER
                      </label>
                    </span>
                    <span className={styles.col1}>
                      <InputWrapper>
                        <CustomTextField
                          className={clsx(styles.inputCreateAccount, errors?.dnBNumber && styles.error)}
                          type='text'
                          mode="wholeNumber"  
                          register={register("dnBNumber")}
                          placeholder=''
                          maxLength={9}
                          onChange={(e: any) => {
                            register("dnBNumber").onChange(e)
                            const dnb = e.target.value.replace(/\D/g, '');
                            setValue('dnBNumber', dnb);
                          }}
                        />
                      </InputWrapper>
                    </span>
                  </div>
                  <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                      <label className={clsx(isInputFocused.einNumber && styles.focusLbl)} htmlFor="einNumber">
                        COMPANY EIN
                      </label>
                    </span>
                    <span className={styles.col1}>
                      <InputWrapper>
                        <CustomTextField
                          className={clsx(styles.inputCreateAccount, errors?.einNumber && styles.error)}
                          type='text'
                          register={register("einNumber")}
                          placeholder=''
                          maxLength={10}
                          onChange={(e) => {
                            register("einNumber").onChange(e)
                            handleEinNoChange(e)
                          }}
                          onKeyDown={(e) => {
                            if(e.key === 'Tab' && !e.shiftKey){
                              e.preventDefault();
                              setTimeout(() => {
                                setEditModeDesiredCreditLine(true)
                                const creditLineInput = document.querySelector(`input[id="desired-credit-line"]`);
                                if (creditLineInput instanceof HTMLElement) {
                                  creditLineInput.focus();
                                }
                              }, 100);
                            }
                          }}
                        />
                      </InputWrapper>
                    </span>
                  </div>
                  <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
                    <span className={styles.col1}>
                      <label className={clsx(isInputFocused.creditLine && styles.focusLbl)} htmlFor="creditLine">
                        DESIRED CREDIT LIMIT
                      </label>
                    </span>
                    <span className={styles.col1}>
                        {editModeDesiredCreditLine ? (
                          <ClickAwayListener
                            onClickAway={() => setEditModeDesiredCreditLine(false)}
                          >
                            <input
                              id='desired-credit-line'
                              type="string"
                              className={clsx(styles.inputCreateAccount, errors?.creditLine && styles.error)}
                              autoFocus={true}
                              value={
                                (+watch("creditLine")) ?
                                  (formatCurrencyWithComma(watch("creditLine")) ?? "") :
                                  (watch("creditLine") ?? "")
                              }
                              onChange={(e) => {
                                requestCreditLineChangeHandler(e, "creditLine")
                              }}
                              onBlur={() => {
                                setEditModeDesiredCreditLine(false)
                              }}
                            />

                          </ClickAwayListener>
                        ) : (
                          <span className={clsx(styles.inputCreateAccount, errors?.creditLine && styles.error)}  onClick={() => setEditModeDesiredCreditLine(true)}>
                            $ { watch("creditLine") ? formatToTwoDecimalPlaces(watch("creditLine") ) : ''}
                          </span>
                        )}
                
                    </span>
                  </div>
                </>
              )

          }


        </div>
        <div className={styles.formGroupInputContainer}>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={styles.paymentHeader} htmlFor="bankName">
                CREDIT/DEBIT CARD
              </label>
            </span>
          </div>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.bankName && styles.focusLbl)} htmlFor="bankName">
                CARD TYPE
              </label>
            </span>
            <span className={styles.col1}>
              <span className={clsx(styles.inputCreateAccount)}>{watch('cardType') || ''}</span>
            </span>
          </div>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.accountName && styles.focusLbl)} htmlFor="accountName">
                CARD NUMBER
              </label>
            </span>
            <span className={styles.col1}>
              <div className={styles.stripeElement}>

                <CardNumberElement options={{
                  style: stripeElementsStyle,
                  placeholder: watch('cardNumberLast4Digits') ? `**** **** **** ${watch('cardNumberLast4Digits')}` : '0000 0000 0000 0000'

                }}
                  onChange={(e) => handleCardChange(e, 'cardNumber')}
                  onBlur={() => handleCardSubmit()}
                />
              </div>
            </span>
          </div>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.bankRoutingNumber && styles.focusLbl)} htmlFor="bankRoutingNumber">
                EXPIRATION / CVV
              </label>
            </span>
            <span className={clsx(styles.col1, styles.stripePaymentGrid)}>
              <div className={styles.stripeElement}>
                <CardExpiryElement options={{
                  style: stripeElementsStyle,
                  placeholder: watch('cardExpiry') ? `${watch('cardExpiry')}` : 'MM / YY'
                }}
                  onChange={(e) => handleCardChange(e, 'cardExpiry')}
                  onBlur={() => handleCardSubmit()}
                />
              </div>
              <div className={styles.stripeElement}>
                <CardCvcElement options={{
                  style: stripeElementsStyle,
                  placeholder: 'CVV'
                }}
                  onChange={(e) => handleCardChange(e, 'cardCvv')}
                  onBlur={() => handleCardSubmit()}
                />
              </div>
            </span>
          </div>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.billingZipCode && styles.focusLbl)} htmlFor="billingZipCode">
                BILLING ZIP CODE
              </label>
            </span>
            <span className={styles.col1}>
              <InputWrapper>
                <CustomTextField
                  className={clsx(styles.inputCreateAccount, errors?.billingZipCode && styles.error)}
                  type='text'
                  register={register("billingZipCode")}
                  placeholder='XXXXX'
                  maxLength={5}
                  mode="wholeNumber"
                  onBlur={() => handleCardSubmit()}
                />
              </InputWrapper>
            </span>
          </div>
        </div>
        <div className={styles.formGroupInputContainer}>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={styles.paymentHeader} htmlFor="bankName">
                CASH IN ADVANCE
              </label>
            </span>
            <span className={styles.col1}>
              <button className={styles.headerBtn}><span onClick={handleRevealCashAdvance}>CLICK TO REVEAL</span></button>
            </span>
          </div>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.bankName && styles.focusLbl)} htmlFor="bankName">
                BANK NAME
              </label>
            </span>
            <span className={styles.col1}>
              <span className={styles.inputCreateAccount}>
                {watch('bankName')}
              </span>
            </span>
          </div>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.accountName && styles.focusLbl)} htmlFor="accountName">
                ACCOUNT NAME
              </label>
            </span>
            <span className={styles.col1}>
                <span className={styles.inputCreateAccount}>
                  {watch('accountName')}
                </span>
            </span>
          </div>
          <div className={styles.formGroupInput}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.bankRoutingNumber && styles.focusLbl)} htmlFor="bankRoutingNumber">
                BANK ROUTING NUMBER
              </label>
            </span>
            <span className={styles.col1}>
                <span className={styles.inputCreateAccount}>
                  { showMaskedAchDetails ? maskedRoutingNo : watch('refRoutingNo')}
                </span>
            </span>
          </div>
          <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
            <span className={styles.col1}>
              <label className={clsx(isInputFocused.bankAccountNumber && styles.focusLbl)} htmlFor="bankAccountNumber">
                BANK ACCOUNT NUMBER
              </label>
            </span>
            <span className={styles.col1}>
                <span className={styles.inputCreateAccount}>
                  { showMaskedAchDetails ? maskedAccountNo : watch('refAccountNo')}
                </span>
            </span>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
};

export default PaymentsTab; 