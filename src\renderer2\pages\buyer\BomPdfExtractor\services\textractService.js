import axios from 'axios';
import { API_CONFIG } from '../config';
import { v4 as uuidv4 } from 'uuid';
import { commomKeys, getChannelWindow, useGlobalStore } from '@bryzos/giss-ui-library';
import useDialogStore from '../../../../component/DialogPopup/DialogStore';

/**
 * Service for handling AWS Textract operations
 */
class TextractService {
  /**
   * Upload a PDF file to S3 via the backend API
   * @param {File} pdfFile - The PDF file to upload
   * @returns {Promise<Object>} - Response with S3 URL
   */
  async uploadPdf(pdfFile) {
    try {
      //getSignedurl pay load
      const extension = pdfFile.name.split('.').pop();
      const signedUrlPayload = {
        data: {
          "bucket_name": import.meta.env.VITE_S3_UPLOAD_BOM_BUCKET_NAME,
          "object_key": import.meta.env.VITE_ENVIRONMENT + '/bom/' + 'BOM-' + uuidv4() + '.' + extension,
          "expire_time": 3000
        }
      };
        const signedUrlResponse = await axios.post(`${import.meta.env.VITE_API_SERVICE}/user/get_signed_url`, signedUrlPayload);
      
        if (
          typeof signedUrlResponse.data.data === "object" &&
          "error_message" in signedUrlResponse.data.data
        ) {
          throw new Error(signedUrlResponse.data.data.error_message);
        }
  
        const signedUrl = signedUrlResponse.data.data;
        console.log('signedUrl',signedUrl)
        const uploadResponse = await axios.put(signedUrl, pdfFile, {
            headers: {
                'Content-Type': pdfFile.type,
            }

        });

        if (
          typeof uploadResponse.data.data === "object" &&
          "error_message" in uploadResponse.data.data
        ) {
          throw new Error(uploadResponse.data.data.error_message);
        }
  
        console.log('uploadResponse',uploadResponse)
        console.log('signedUrlResponse',signedUrlResponse)
        return {s3Url: signedUrlResponse.data.data.split('?')[0], object_key: signedUrlPayload.data.object_key};

      
    } catch (error) {
      throw error;
    }
  }

  /**
   * Process a PDF file with AWS Textract
   * @param {string} s3Url - The S3 URL of the PDF file
   * @returns {Promise<Object>} - Textract analysis results
   */
  async processPdf(payload) {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_SERVICE}/user/bom/extract-text-from-file`,
        {data:payload}
      );

      if (
        typeof response.data.data === "object" &&
        "error_message" in response.data.data
      ) {
        throw new Error(response.data.data.error_message);
      }

      return response.data;

      // Process the uploaded file with Textract
      // const response = await fetch(
      //   `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.processPdf}`,
      //   {
      //     method: 'POST',
      //     headers: {
      //       'Content-Type': 'application/json',
      //     },
      //     body: JSON.stringify({ s3Url }),
      //     timeout: API_CONFIG.timeouts.processing,
      //   }
      // );

      // if (!response.ok) {
      //   const errorData = await response.json();
      //   throw new Error(errorData.error || `Processing failed: ${response.statusText}`);
      // }
      // const data = await response.json();
      // console.log('Textract data:', data);
      // return data;
      
    } catch (error) {
      throw error;
    }
  }

  async getBomData(bomUploadID){
    const { showCommonDialog, resetDialogStore } = useDialogStore.getState();
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_API_SERVICE}/user/bom/${bomUploadID}`
      );

      if (
        typeof response.data.data === "object" &&
        "error_message" in response.data.data
      ) {
        throw new Error(response.data.data.error_message);
      }

      return response.data;
    } catch (error) {
      console.error(error)
      showCommonDialog(null,error?.message || commomKeys.errorContent, null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: resetDialogStore}])
      throw error;
    }
  }

  async saveExtractedData(payload){
    const { showCommonDialog, resetDialogStore } = useDialogStore.getState();
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_SERVICE}/user/bom/product-extract`,
        {data:payload}
      );

      if (
        typeof response.data.data === "object" &&
        "error_message" in response.data.data
      ) {
        throw new Error(response.data.data.error_message);
      }

      return response.data;
    } catch (error) {
      showCommonDialog(null,error?.message || commomKeys.errorContent, null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: resetDialogStore}])
      throw error;
    }
  }

  async saveGeometryData(payload){
    const { showCommonDialog, resetDialogStore } = useDialogStore.getState();
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_SERVICE}/user/bom/selection-box-details`,
        {data:payload}
      );

      if (
        typeof response.data.data === "object" &&
        "error_message" in response.data.data
      ) {
        throw new Error(response.data.data.error_message);
      }

      return response.data;
    } catch (error) {
      showCommonDialog(null,error?.message || commomKeys.errorContent, null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: resetDialogStore}])
      throw error;
    }
  }

  /**
   * Upload and process a PDF file in one operation
   * @param {File} pdfFile - The PDF file to upload and process
   * @returns {Promise<Object>} - Textract analysis results
   */
  async uploadAndProcessPdf(pdfFile, uploadBomInitialData, isImageBased) {
    const { showCommonDialog, resetDialogStore } = useDialogStore.getState();
    try {
      // Step 1: Upload the PDF file
      const uploadData = await this.uploadPdf(pdfFile);



      let deviceId;
      const channelWindow = getChannelWindow();
      if (channelWindow?.getDeviceId) {
        deviceId = window.electron.sendSync({ channel: channelWindow.getDeviceId });
    }else{
      deviceId = "No device_id found"
    }

      const payload={
        'is_textract': isImageBased,
        "actual_file_name": pdfFile.name,
        "object_key": uploadData.object_key,
        "s3_url": uploadData.s3Url,
        "device_id": deviceId,
        "delivery_date": uploadBomInitialData.delivery_date,
        "shipping_details": {
            "line1": uploadBomInitialData.shipping_details.line1,
            "line2":  uploadBomInitialData.shipping_details.line2,
            "city": uploadBomInitialData.shipping_details.city,
            "state_id": uploadBomInitialData.shipping_details.state_id,
            "zip": uploadBomInitialData.shipping_details.zip
        },
        "bom_type": uploadBomInitialData.order_type,
        "bom_name": uploadBomInitialData.internal_po_number
    }

      // Step 2: Process the uploaded file with Textract
      const responseData = await this.processPdf(payload);
      const {Blocks,DocumentMetadata} = responseData.data;
      let textractData = null;
      if(isImageBased){
        textractData = {Blocks,DocumentMetadata};
      }
      return {textractData:textractData, s3Url: uploadData.s3Url, bomUploadID: responseData.data.bom_upload_id };
    } catch (error) {
      console.error(error)
      showCommonDialog(null,error?.message || commomKeys.errorContent, null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: resetDialogStore}])
      throw error;
    }
  }
}

// Create and export a singleton instance
const textractService = new TextractService();
export default textractService;
